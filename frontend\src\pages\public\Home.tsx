import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  CloudUpload as CloudUploadIcon,
  AutoAwesome as AutoAwesomeIcon,
  ImportExport as ImportExportIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';

const features = [
  {
    icon: <DescriptionIcon fontSize="large" color="primary" />,
    title: 'Gestão de Documentos',
    description: 'Organize e gerencie todos os seus documentos contábeis em um único lugar, com acesso rápido e seguro.',
  },
  {
    icon: <CloudUploadIcon fontSize="large" color="primary" />,
    title: 'Upload Simplificado',
    description: 'Faça upload de extratos bancários e outros documentos com apenas alguns cliques.',
  },
  {
    icon: <AutoAwesomeIcon fontSize="large" color="primary" />,
    title: 'Processamento Automático',
    description: 'Nosso sistema processa automaticamente seus extratos bancários, identificando e categorizando transações.',
  },
  {
    icon: <ImportExportIcon fontSize="large" color="primary" />,
    title: 'Exportação Flexível',
    description: 'Exporte dados processados para diversos formatos compatíveis com os principais sistemas contábeis.',
  },
  {
    icon: <SecurityIcon fontSize="large" color="primary" />,
    title: 'Segurança Avançada',
    description: 'Seus dados são protegidos com criptografia de ponta a ponta e controles de acesso rigorosos.',
  },
  {
    icon: <SpeedIcon fontSize="large" color="primary" />,
    title: 'Economia de Tempo',
    description: 'Reduza drasticamente o tempo gasto em tarefas manuais e foque no que realmente importa para seu negócio.',
  },
];

const Home: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          pt: { xs: 8, md: 12 },
          pb: { xs: 10, md: 14 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  mb: 2,
                }}
              >
                Simplifique sua gestão contábil
              </Typography>
              <Typography
                variant="h5"
                sx={{
                  mb: 4,
                  fontWeight: 400,
                  opacity: 0.9,
                }}
              >
                Automatize o processamento de documentos e extratos bancários com nossa plataforma inteligente
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: { xs: 'wrap', sm: 'nowrap' } }}>
                <Button
                  variant="contained"
                  color="secondary"
                  size="large"
                  onClick={() => navigate('/planos')}
                  sx={{
                    py: 1.5,
                    px: 3,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    flex: { xs: '1 0 100%', sm: '0 0 auto' },
                    mb: { xs: 2, sm: 0 },
                  }}
                >
                  Ver Planos
                </Button>
                <Button
                  variant="outlined"
                  color="inherit"
                  size="large"
                  onClick={() => navigate('/contato')}
                  sx={{
                    py: 1.5,
                    px: 3,
                    fontSize: '1.1rem',
                    borderColor: 'white',
                    '&:hover': {
                      borderColor: 'white',
                      bgcolor: 'rgba(255, 255, 255, 0.1)',
                    },
                    flex: { xs: '1 0 100%', sm: '0 0 auto' },
                  }}
                >
                  Fale Conosco
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12} md={6} sx={{ display: { xs: 'none', md: 'block' } }}>
              <Box
                sx={{
                  position: 'relative',
                  height: '400px',
                  width: '100%',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: 4,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1">
                  [Imagem ilustrativa do dashboard]
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography variant="h3" component="h2" gutterBottom>
            Recursos Principais
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto' }}>
            Nossa plataforma oferece tudo o que você precisa para otimizar seus processos contábeis
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 2,
                  transition: 'transform 0.3s, box-shadow 0.3s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: 3,
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Box sx={{ mb: 2 }}>{feature.icon}</Box>
                  <Typography variant="h5" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* How It Works Section */}
      <Box sx={{ bgcolor: 'grey.100', py: 8 }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom>
              Como Funciona
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto' }}>
              Um processo simples e eficiente para transformar sua gestão contábil
            </Typography>
          </Box>

          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', borderRadius: 2 }}>
                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                  <Typography
                    variant="h1"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 2,
                    }}
                  >
                    1
                  </Typography>
                  <Typography variant="h5" component="h3" gutterBottom>
                    Upload de Documentos
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Faça upload dos seus extratos bancários e outros documentos contábeis através da nossa plataforma intuitiva.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', borderRadius: 2 }}>
                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                  <Typography
                    variant="h1"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 2,
                    }}
                  >
                    2
                  </Typography>
                  <Typography variant="h5" component="h3" gutterBottom>
                    Processamento Automático
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Nosso sistema processa automaticamente os documentos, identificando e categorizando transações com precisão.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', borderRadius: 2 }}>
                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                  <Typography
                    variant="h1"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 2,
                    }}
                  >
                    3
                  </Typography>
                  <Typography variant="h5" component="h3" gutterBottom>
                    Exportação para Sistemas Contábeis
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Exporte os dados processados em formatos compatíveis com os principais sistemas contábeis do mercado.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Testimonials Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography variant="h3" component="h2" gutterBottom>
            O Que Nossos Clientes Dizem
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto' }}>
            Veja como nossa plataforma tem ajudado empresas e profissionais contábeis
          </Typography>
        </Box>

        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%', borderRadius: 2 }}>
              <CardContent sx={{ py: 4 }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "O Nexlify Contábil revolucionou nossa forma de trabalhar. Reduzimos em 70% o tempo gasto com processamento manual de extratos bancários."
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      bgcolor: 'primary.light',
                      mr: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  >
                    RS
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Ricardo Silva
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Contador, RS Contabilidade
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%', borderRadius: 2 }}>
              <CardContent sx={{ py: 4 }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "A precisão do processamento automático é impressionante. Praticamente eliminou erros humanos na categorização de transações."
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      bgcolor: 'secondary.light',
                      mr: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  >
                    AM
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Ana Martins
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      CEO, Martins Assessoria Contábil
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%', borderRadius: 2 }}>
              <CardContent sx={{ py: 4 }}>
                <Typography variant="body1" paragraph sx={{ fontStyle: 'italic' }}>
                  "Como profissional autônomo, o plano individual atende perfeitamente minhas necessidades. Interface intuitiva e suporte excelente."
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      bgcolor: 'success.light',
                      mr: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  >
                    PO
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Paulo Oliveira
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Contador Autônomo
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom>
            Pronto para começar?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Escolha o plano ideal para o seu negócio e comece a transformar sua gestão contábil hoje mesmo
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: { xs: 'wrap', sm: 'nowrap' } }}>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              onClick={() => navigate('/planos')}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                flex: { xs: '1 0 100%', sm: '0 0 auto' },
                mb: { xs: 2, sm: 0 },
              }}
            >
              Ver Planos
            </Button>
            <Button
              variant="outlined"
              color="inherit"
              size="large"
              onClick={() => navigate('/registro')}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
                flex: { xs: '1 0 100%', sm: '0 0 auto' },
              }}
            >
              Criar Conta Grátis
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default Home; 