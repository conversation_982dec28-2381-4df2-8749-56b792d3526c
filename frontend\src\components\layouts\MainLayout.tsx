import React, { useState } from 'react';
import { Outlet, Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  Button,
  Container,
  Divider,
  Drawer,
  IconButton,
  Link,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import { useAuth } from '../../contexts/AuthContext';

const drawerWidth = 240;

const MainLayout: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Itens do menu público
  const publicNavItems = [
    { name: 'Início', path: '/' },
    { name: 'Planos', path: '/planos' },
    { name: 'Sobre', path: '/sobre' },
    { name: 'Contato', path: '/contato' },
  ];

  // Itens do menu autenticado
  const authNavItems = [
    { name: 'Dashboard', path: '/dashboard' },
    { name: 'Documentos', path: '/dashboard/documentos' },
    { name: 'Clientes', path: '/clientes' },
    ...(user && ['admin', 'manager'].includes(user.role) ? [{ name: 'Usuários', path: '/dashboard/usuarios' }] : []),
    ...(user && ['admin', 'accountant'].includes(user.role) ? [{ name: 'Regras Contábeis', path: '/dashboard/regras-contabeis' }] : []),
  ];

  // Usar os itens de navegação com base no estado de autenticação
  const navItems = isAuthenticated ? authNavItems : publicNavItems;

  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <Box component={RouterLink} to={isAuthenticated ? '/dashboard' : '/'} sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
          <img src="/static/media/LOGOSPNG/logo.png" alt="Nexlify" style={{ height: 40, marginRight: 8 }} />
          <Typography variant="h6" sx={{ color: 'primary.main' }}>
            Nexlify Contábil
          </Typography>
        </Box>
        <IconButton edge="end" color="inherit" aria-label="close drawer" onClick={handleDrawerToggle}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Divider />
      <List>
        {navItems.map((item) => (
          <ListItem key={item.name} disablePadding>
            <ListItemButton
              component={RouterLink}
              to={item.path}
              sx={{ textAlign: 'center' }}
            >
              <ListItemText primary={item.name} />
            </ListItemButton>
          </ListItem>
        ))}
        {isAuthenticated && (
          <ListItem disablePadding>
            <ListItemButton onClick={handleLogout} sx={{ textAlign: 'center' }}>
              <ListItemText primary="Sair" />
            </ListItemButton>
          </ListItem>
        )}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <AppBar position="sticky" color="default" elevation={1}>
        <Container maxWidth="lg">
          <Toolbar disableGutters sx={{ justifyContent: 'space-between' }}>
            <Box component={RouterLink} to={isAuthenticated ? '/dashboard' : '/'} sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
              <img src="/static/media/LOGOSPNG/logo.png" alt="Nexlify" style={{ height: 40, marginRight: 8 }} />
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 700,
                  color: 'primary.main',
                }}
              >
                Nexlify Contábil
              </Typography>
            </Box>

            <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
              {navItems.map((item) => (
                <Button
                  key={item.name}
                  component={RouterLink}
                  to={item.path}
                  color="inherit"
                >
                  {item.name}
                </Button>
              ))}
              {isAuthenticated ? (
                <Button
                  color="inherit"
                  onClick={handleLogout}
                >
                  Sair
                </Button>
              ) : (
                <>
                  <Button
                    component={RouterLink}
                    to="/login"
                    color="inherit"
                  >
                    Entrar
                  </Button>
                  <Button
                    component={RouterLink}
                    to="/registro"
                    variant="contained"
                    color="primary"
                  >
                    Registrar
                  </Button>
                </>
              )}
            </Box>

            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="end"
              onClick={handleDrawerToggle}
              sx={{ display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
          </Toolbar>
        </Container>
      </AppBar>

      <Box component="nav">
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box component="main" sx={{ flexGrow: 1 }}>
        <Outlet />
      </Box>

      {!isAuthenticated && (
        <Box
          component="footer"
          sx={{
            py: 3,
            px: 2,
            mt: 'auto',
            backgroundColor: (theme) =>
              theme.palette.mode === 'light'
                ? theme.palette.grey[200]
                : theme.palette.grey[800],
          }}
        >
          <Container maxWidth="lg">
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'center', md: 'flex-start' },
                textAlign: { xs: 'center', md: 'left' },
              }}
            >
              <Box sx={{ mb: { xs: 3, md: 0 } }}>
                <Box component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', mb: 1 }}>
                  <img src="/static/media/LOGOSPNG/logo.png" alt="Nexlify" style={{ height: 40, marginRight: 8 }} />
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                    }}
                  >
                    Nexlify Contábil
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Sistema de Gestão de Documentos e Processamento Contábil
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  © {new Date().getFullYear()} Nexlify Contábil. Todos os direitos reservados.
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Links Rápidos
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  {publicNavItems.map((item) => (
                    <Link
                      key={item.name}
                      component={RouterLink}
                      to={item.path}
                      color="inherit"
                      underline="hover"
                      sx={{ mb: 0.5 }}
                    >
                      {item.name}
                    </Link>
                  ))}
                </Box>
              </Box>

              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Contato
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  <EMAIL>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  (11) 9999-9999
                </Typography>
              </Box>
            </Box>
          </Container>
        </Box>
      )}
    </Box>
  );
};

export default MainLayout; 