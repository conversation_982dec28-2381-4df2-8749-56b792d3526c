require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');

// Importação das rotas
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const planRoutes = require('./routes/plan.routes');
const documentRoutes = require('./routes/document.routes');
const clientRoutes = require('./routes/client.routes');
const accountingRuleRoutes = require('./routes/accounting-rule.routes');
// Rotas que ainda não foram implementadas
// const companyRoutes = require('./routes/company.routes');

// Importação dos middlewares
const { errorHandler } = require('./utils/error');

// Importação da conexão com o banco de dados
const { sequelize } = require('./models');

const app = express();
const PORT = process.env.PORT || 5000;

// Middlewares
// Configuração CORS
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3003',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Testar conexão com o MySQL
sequelize.authenticate()
  .then(() => {
    console.log('Conexão com MySQL estabelecida com sucesso');
  })
  .catch((err) => {
    console.error('Erro ao conectar ao MySQL:', err);
    process.exit(1);
  });

// Rotas
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/plans', planRoutes);
app.use('/api/documents', documentRoutes);
app.use('/api/clients', clientRoutes);
app.use('/api/accounting-rules', accountingRuleRoutes);
// Rotas que ainda não foram implementadas
// app.use('/api/companies', companyRoutes);

// Rota de teste
app.get('/', (req, res) => {
  res.json({ message: 'API do Nexlify Contábil funcionando!' });
});

// Middleware de tratamento de erros
app.use(errorHandler);

// Iniciar o servidor
app.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
});

module.exports = app;