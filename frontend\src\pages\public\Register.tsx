import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  Divider,
  FormControlLabel,
  Checkbox,
  Link,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { authService } from '../../services/api';

const Register = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectedPlanType = queryParams.get('plano');

  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  // Dados pessoais
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // Dados da empresa
  const [companyName, setCompanyName] = useState('');
  const [cnpj, setCnpj] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [postalCode, setPostalCode] = useState('');
  
  // Dados do plano
  const [planType, setPlanType] = useState(selectedPlanType || '');

  // Efeito para definir o plano selecionado quando o componente é montado
  useEffect(() => {
    if (selectedPlanType) {
      setPlanType(selectedPlanType);
    }
  }, [selectedPlanType]);

  const steps = ['Dados Pessoais', 'Dados da Empresa', 'Confirmação'];

  const handleNext = () => {
    if (activeStep === 0) {
      // Validar dados pessoais
      if (!name || !email || !phone || !password || !confirmPassword) {
        setError('Por favor, preencha todos os campos obrigatórios.');
        return;
      }
      
      if (password !== confirmPassword) {
        setError('As senhas não coincidem.');
        return;
      }
      
      if (password.length < 8) {
        setError('A senha deve ter pelo menos 8 caracteres.');
        return;
      }
    } else if (activeStep === 1) {
      // Validar dados da empresa
      if (!companyName || !cnpj || !address || !city || !state || !postalCode) {
        setError('Por favor, preencha todos os campos obrigatórios.');
        return;
      }
      
      if (!planType) {
        setError('Por favor, selecione um plano.');
        return;
      }
    }
    
    setError('');
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!termsAccepted) {
      setError('Você precisa aceitar os termos de uso e política de privacidade.');
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const userData = {
        name,
        email,
        phone,
        password,
        company: {
          name: companyName,
          cnpj,
          address,
          city,
          state,
          postalCode,
        },
        planType,
      };
      
      const response = await authService.register(userData);
      
      if (response.success) {
        // Redirecionar para a página de confirmação
        navigate('/registro-sucesso', { 
          state: { 
            email, 
            planType 
          } 
        });
      } else {
        setError(response.message || 'Erro ao criar conta. Por favor, tente novamente.');
      }
    } catch (err) {
      console.error('Erro ao registrar:', err);
      setError('Erro ao criar conta. Por favor, tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const formatCNPJ = (value: string) => {
    // Remove todos os caracteres não numéricos
    const numericValue = value.replace(/\D/g, '');
    
    // Aplica a máscara de CNPJ: XX.XXX.XXX/XXXX-XX
    return numericValue
      .replace(/^(\d{2})(\d)/, '$1.$2')
      .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
      .replace(/\.(\d{3})(\d)/, '.$1/$2')
      .replace(/(\d{4})(\d)/, '$1-$2')
      .substring(0, 18);
  };

  const formatPhone = (value: string) => {
    // Remove todos os caracteres não numéricos
    const numericValue = value.replace(/\D/g, '');
    
    // Aplica a máscara de telefone: (XX) XXXXX-XXXX
    return numericValue
      .replace(/^(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{5})(\d)/, '$1-$2')
      .substring(0, 15);
  };

  const formatCEP = (value: string) => {
    // Remove todos os caracteres não numéricos
    const numericValue = value.replace(/\D/g, '');
    
    // Aplica a máscara de CEP: XXXXX-XXX
    return numericValue
      .replace(/^(\d{5})(\d)/, '$1-$2')
      .substring(0, 9);
  };

  const handleCNPJChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCnpj(formatCNPJ(e.target.value));
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhone(formatPhone(e.target.value));
  };

  const handleCEPChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPostalCode(formatCEP(e.target.value));
  };

  const getPlanName = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Plano Individual';
      case 'business_basic':
        return 'Plano Empresarial Básico';
      case 'business_plus':
        return 'Plano Empresarial Plus';
      case 'business_premium':
        return 'Plano Empresarial Premium';
      default:
        return 'Plano não selecionado';
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Dados Pessoais
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome Completo"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="E-mail"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Telefone"
                  value={phone}
                  onChange={handlePhoneChange}
                  required
                  placeholder="(00) 00000-0000"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Senha"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Confirmar Senha"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Dados da Empresa
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome da Empresa"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="CNPJ"
                  value={cnpj}
                  onChange={handleCNPJChange}
                  required
                  placeholder="00.000.000/0000-00"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="CEP"
                  value={postalCode}
                  onChange={handleCEPChange}
                  required
                  placeholder="00000-000"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Endereço"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cidade"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Estado"
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Plano Selecionado
                </Typography>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                  <Typography variant="h6" color="primary">
                    {getPlanName(planType)}
                  </Typography>
                  {!planType && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2" color="error">
                        Nenhum plano selecionado. 
                      </Typography>
                      <Button 
                        size="small" 
                        color="primary" 
                        onClick={() => navigate('/planos')}
                        sx={{ mt: 1 }}
                      >
                        Selecionar um plano
                      </Button>
                    </Box>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Confirme seus dados
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Nome:</Typography>
                <Typography variant="body1" gutterBottom>{name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">E-mail:</Typography>
                <Typography variant="body1" gutterBottom>{email}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Telefone:</Typography>
                <Typography variant="body1" gutterBottom>{phone}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Empresa:</Typography>
                <Typography variant="body1" gutterBottom>{companyName}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">CNPJ:</Typography>
                <Typography variant="body1" gutterBottom>{cnpj}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Endereço:</Typography>
                <Typography variant="body1" gutterBottom>{address}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Cidade/Estado:</Typography>
                <Typography variant="body1" gutterBottom>{city}/{state}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">CEP:</Typography>
                <Typography variant="body1" gutterBottom>{postalCode}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">Plano Selecionado:</Typography>
                <Typography variant="body1" color="primary" gutterBottom>
                  {getPlanName(planType)}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={termsAccepted}
                      onChange={(e) => setTermsAccepted(e.target.checked)}
                      color="primary"
                    />
                  }
                  label={
                    <Typography variant="body2">
                      Li e concordo com os{' '}
                      <Link href="/termos" target="_blank">
                        Termos de Uso
                      </Link>{' '}
                      e{' '}
                      <Link href="/privacidade" target="_blank">
                        Política de Privacidade
                      </Link>
                    </Typography>
                  }
                />
              </Grid>
            </Grid>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Crie sua conta
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Preencha os dados abaixo para começar a usar o Nexlify Contábil
          </Typography>
        </Box>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          {renderStepContent(activeStep)}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              disabled={activeStep === 0 || loading}
              onClick={handleBack}
              variant="outlined"
            >
              Voltar
            </Button>
            
            {activeStep === steps.length - 1 ? (
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading || !termsAccepted}
              >
                {loading ? <CircularProgress size={24} /> : 'Criar Conta'}
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
                disabled={loading}
              >
                Próximo
              </Button>
            )}
          </Box>
        </form>

        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2">
            Já tem uma conta?{' '}
            <Link href="/login" underline="hover">
              Faça login
            </Link>
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default Register; 