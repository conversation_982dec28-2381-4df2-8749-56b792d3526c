import api from './api';

export interface Client {
  id: string;
  name: string;
  documentType: 'cpf' | 'cnpj';
  documentNumber: string;
  email: string;
  phone?: string;
  address?: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  company: string;
  accountManager?: string;
  documentTypes: string[];
  documentCount: number;
  transactionCount: number;
  storageUsed: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClientFilter {
  search?: string;
  documentType?: 'cpf' | 'cnpj';
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface CreateClientData {
  name: string;
  documentType: 'cpf' | 'cnpj';
  documentNumber: string;
  email: string;
  phone?: string;
  address?: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  documentTypes: string[];
}

export interface UpdateClientData {
  name?: string;
  email?: string;
  phone?: string;
  address?: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  documentTypes?: string[];
  isActive?: boolean;
}

const clientService = {
  /**
   * Lista clientes com filtros opcionais
   * @param filters Filtros para a listagem
   * @returns Promise com a lista de clientes
   */
  async getClients(filters?: ClientFilter): Promise<{
    clients: Client[];
    total: number;
    page: number;
    limit: number;
  }> {
    const response = await api.get('/clients', { params: filters });
    return response.data;
  },

  /**
   * Obtém um cliente pelo ID
   * @param id ID do cliente
   * @returns Promise com o cliente
   */
  async getClientById(id: string): Promise<Client> {
    const response = await api.get(`/clients/${id}`);
    return response.data;
  },

  /**
   * Cria um novo cliente
   * @param clientData Dados do cliente
   * @returns Promise com o cliente criado
   */
  async createClient(clientData: CreateClientData): Promise<Client> {
    const response = await api.post('/clients', clientData);
    return response.data;
  },

  /**
   * Atualiza um cliente existente
   * @param id ID do cliente
   * @param clientData Dados do cliente
   * @returns Promise com o cliente atualizado
   */
  async updateClient(id: string, clientData: UpdateClientData): Promise<Client> {
    const response = await api.put(`/clients/${id}`, clientData);
    return response.data;
  },

  /**
   * Ativa ou desativa um cliente
   * @param id ID do cliente
   * @param isActive Status de ativação
   * @returns Promise com o cliente atualizado
   */
  async toggleClientStatus(id: string, isActive: boolean): Promise<Client> {
    const response = await api.patch(`/clients/${id}/status`, { isActive });
    return response.data;
  },

  /**
   * Exclui um cliente
   * @param id ID do cliente
   * @returns Promise vazia em caso de sucesso
   */
  async deleteClient(id: string): Promise<void> {
    await api.delete(`/clients/${id}`);
  }
};

export default clientService; 