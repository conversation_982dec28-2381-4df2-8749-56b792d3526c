{"name": "nexlify-contabil-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.3", "@types/node": "^20.4.5", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "axios": "^1.4.0", "date-fns": "^2.30.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "typescript": "^5.1.6", "web-vitals": "^3.4.0"}, "scripts": {"start": "webpack serve --mode development --config webpack.config.js", "build": "webpack --mode production --config webpack.config.js", "test": "jest", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.8.1", "dotenv-webpack": "^8.0.1", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "http-proxy-middleware": "^3.0.3", "jest": "^29.6.1", "jest-environment-jsdom": "^29.6.1", "mini-css-extract-plugin": "^2.7.6", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}