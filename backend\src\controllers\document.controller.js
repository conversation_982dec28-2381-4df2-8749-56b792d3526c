const { Document, User, Company } = require('../models');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const unlinkAsync = promisify(fs.unlink);
const { createError } = require('../utils/error');

// Diretório para armazenar os arquivos
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// Garantir que o diretório de uploads exista
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Upload de documentos
 * @route POST /api/documents/upload
 * @access Private
 */
exports.uploadDocuments = async (req, res, next) => {
  try {
    // Verificar se há arquivos enviados
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo enviado'
      });
    }

    // Verificar se o usuário está autenticado
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'Usuário não autenticado'
      });
    }

    // Extrair dados do corpo da requisição
    const { user_id } = req.body;
    let metadata = {};

    try {
      metadata = req.body.metadata ? JSON.parse(req.body.metadata) : {};
    } catch (error) {
      console.error('Erro ao parsear metadata:', error);
      metadata = {};
    }

    // Verificar se o usuário existe (se um ID de usuário específico foi fornecido)
    let user;
    if (user_id) {
      user = await User.findByPk(user_id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'Usuário não encontrado'
        });
      }
    }

    // Obter a empresa do usuário logado
    const company = await Company.findOne({
      where: { id: req.user.company_id }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Empresa não encontrada'
      });
    }

    const uploadedDocuments = [];

    // Processar cada arquivo
    for (const file of req.files) {
      try {
        // Determinar o tipo de documento com base no MIME type
        let documentType = 'outro';
        if (file.mimetype.includes('pdf')) {
          documentType = metadata.file_type || 'extrato_bancario';
        } else if (file.mimetype.includes('image')) {
          documentType = metadata.file_type || 'recibo';
        } else if (file.mimetype.includes('excel') || file.mimetype.includes('spreadsheet')) {
          documentType = metadata.file_type || 'folha_pagamento';
        }

        // Verificar se o arquivo existe
        if (!fs.existsSync(file.path)) {
          console.error(`Arquivo não encontrado: ${file.path}`);
          continue; // Pular para o próximo arquivo
        }

        // Criar o documento no banco de dados
        const document = await Document.create({
          name: metadata.name || file.originalname,
          description: metadata.description || '',
          file_type: documentType,
          user_id: user_id || req.user.id,
          company_id: company.id,
          status: 'pending',
          file_path: file.path,
          file_size: file.size,
          file_type: file.mimetype,
          metadata: {
            original_filename: file.originalname,
            period: metadata.period || {
              month: new Date().getMonth() + 1,
              year: new Date().getFullYear()
            }
          }
        });

        uploadedDocuments.push(document);
      } catch (error) {
        console.error(`Erro ao processar arquivo ${file.originalname}:`, error);
        // Continuar processando os outros arquivos mesmo se um falhar
      }
    }

    // Verificar se algum documento foi criado com sucesso
    if (uploadedDocuments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum documento foi processado com sucesso'
      });
    }

    // Retornar resposta de sucesso
    res.status(201).json({
      success: true,
      count: uploadedDocuments.length,
      data: uploadedDocuments,
      message: `${uploadedDocuments.length} documento(s) enviado(s) com sucesso`
    });
  } catch (error) {
    console.error('Erro no upload de documentos:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao processar upload de documentos',
      error: error.message
    });
  }
};

/**
 * Listar documentos
 * @route GET /api/documents
 * @access Private
 */
exports.getDocuments = async (req, res, next) => {
  try {
    const {
      status,
      type,
      user_id,
      start_date,
      end_date,
      page = 1,
      limit = 10
    } = req.query;

    // Construir o filtro
    const where = {
      company_id: req.user.company_id
    };

    // Aplicar filtros adicionais
    if (status) where.status = status;
    if (type) where.file_type = type;
    if (user_id) where.user_id = user_id;

    // Filtrar por data
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) where.created_at.$gte = new Date(start_date);
      if (end_date) where.created_at.$lte = new Date(end_date);
    }

    // Calcular paginação
    const offset = (page - 1) * limit;

    // Buscar documentos
    const { count, rows: documents } = await Document.findAndCountAll({
      where,
      include: [
        { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    res.status(200).json({
      success: true,
      count: documents.length,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      data: documents
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Obter um documento pelo ID
 * @route GET /api/documents/:id
 * @access Private
 */
exports.getDocumentById = async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id, {
      include: [
        { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ]
    });

    if (!document) {
      return next(createError(404, 'Documento não encontrado'));
    }

    // Verificar se o usuário tem acesso ao documento
    if (document.company_id !== req.user.company_id) {
      return next(createError(403, 'Acesso negado'));
    }

    res.status(200).json({
      success: true,
      data: document
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Atualizar o status de um documento
 * @route PATCH /api/documents/:id/status
 * @access Private
 */
exports.updateDocumentStatus = async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!status) {
      return next(createError(400, 'Status é obrigatório'));
    }

    const document = await Document.findByPk(req.params.id);

    if (!document) {
      return next(createError(404, 'Documento não encontrado'));
    }

    // Verificar se o usuário tem acesso ao documento
    if (document.company_id !== req.user.company_id) {
      return next(createError(403, 'Acesso negado'));
    }

    // Preparar os dados para atualização
    const updateData = {
      status,
      processing_result: document.processing_result || {}
    };

    // Atualizar campos adicionais com base no status
    if (status === 'approved' || status === 'rejected') {
      updateData.processing_result.reviewed_by = req.user.id;
      updateData.processing_result.reviewed_at = new Date();

      if (status === 'rejected' && req.body.rejection_reason) {
        updateData.processing_result.rejection_reason = req.body.rejection_reason;
      }
    } else if (status === 'processed') {
      updateData.processing_result.processed_at = new Date();
    }

    await document.update(updateData);

    res.status(200).json({
      success: true,
      data: document
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Excluir um documento
 * @route DELETE /api/documents/:id
 * @access Private
 */
exports.deleteDocument = async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);

    if (!document) {
      return next(createError(404, 'Documento não encontrado'));
    }

    // Verificar se o usuário tem acesso ao documento
    if (document.company_id !== req.user.company_id) {
      return next(createError(403, 'Acesso negado'));
    }

    // Excluir o arquivo físico
    if (document.file_path) {
      try {
        await unlinkAsync(document.file_path);
      } catch (error) {
        console.error('Erro ao excluir arquivo:', error);
      }
    }

    // Excluir o documento do banco de dados
    await document.destroy();

    res.status(200).json({
      success: true,
      message: 'Documento excluído com sucesso'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Download de um documento
 * @route GET /api/documents/:id/download
 * @access Private
 */
exports.downloadDocument = async (req, res, next) => {
  try {
    const document = await Document.findById(req.params.id);

    if (!document) {
      return next(createError(404, 'Documento não encontrado'));
    }

    // Verificar se o usuário tem acesso ao documento
    const company = await Company.findOne({ owner: req.user.id });
    if (!company || document.company.toString() !== company._id.toString()) {
      return next(createError(403, 'Acesso negado'));
    }

    // Verificar se o arquivo existe
    if (!document.filePath || !fs.existsSync(document.filePath)) {
      return next(createError(404, 'Arquivo não encontrado'));
    }

    // Enviar o arquivo
    res.download(document.filePath, document.originalFilename);
  } catch (error) {
    next(error);
  }
};

/**
 * Processar um documento (OCR, extração de dados, etc.)
 * @route POST /api/documents/:id/process
 * @access Private
 */
exports.processDocument = async (req, res, next) => {
  try {
    const document = await Document.findById(req.params.id);

    if (!document) {
      return next(createError(404, 'Documento não encontrado'));
    }

    // Verificar se o usuário tem acesso ao documento
    const company = await Company.findOne({ owner: req.user.id });
    if (!company || document.company.toString() !== company._id.toString()) {
      return next(createError(403, 'Acesso negado'));
    }

    // Verificar se o documento já está sendo processado
    if (document.status === 'em_analise') {
      return next(createError(400, 'Documento já está em processamento'));
    }

    // Atualizar o status para "em_analise"
    document.status = 'em_analise';
    await document.save();

    // Aqui seria implementada a lógica de processamento OCR
    // Por enquanto, apenas simulamos o processamento

    // Simular processamento assíncrono
    setTimeout(async () => {
      try {
        // Atualizar o documento com os dados processados
        document.status = 'processado';
        document.processedAt = Date.now();
        document.processedData = {
          // Dados simulados
          extractedText: 'Texto extraído do documento',
          transactions: [
            { date: new Date(), description: 'Transação 1', value: 100.0 },
            { date: new Date(), description: 'Transação 2', value: -50.0 }
          ]
        };
        document.transactionCount = 2;

        await document.save();
        console.log(`Documento ${document._id} processado com sucesso`);
      } catch (error) {
        console.error(`Erro ao processar documento ${document._id}:`, error);
        // Em caso de erro, atualizar o status para "pendente"
        document.status = 'pendente';
        await document.save();
      }
    }, 5000); // Simular 5 segundos de processamento

    res.status(202).json({
      success: true,
      message: 'Documento enviado para processamento',
      data: document
    });
  } catch (error) {
    next(error);
  }
};