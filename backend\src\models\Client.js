const { Model, DataTypes } = require('sequelize');

class Client extends Model {
  static init(sequelize) {
    super.init({
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      document_type: {
        type: DataTypes.ENUM('cpf', 'cnpj'),
        allowNull: false
      },
      document_number: {
        type: DataTypes.STRING(14),
        allowNull: false,
        unique: true,
        validate: {
          notEmpty: true,
          len: [11, 14]
        }
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          isEmail: true
        }
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true
      },
      address_street: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_number: {
        type: DataTypes.STRING(20),
        allowNull: true
      },
      address_complement: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_neighborhood: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_city: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_state: {
        type: DataTypes.CHAR(2),
        allowNull: true
      },
      address_zip_code: {
        type: DataTypes.STRING(8),
        allowNull: true
      },
      company_id: {
        type: DataTypes.UUID,
        allowNull: false
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      }
    }, {
      sequelize,
      tableName: 'clients',
      underscored: true
    });
  }

  static associate(models) {
    this.belongsTo(models.Company, { foreignKey: 'company_id', as: 'company' });
    this.hasMany(models.Document, { foreignKey: 'client_id', as: 'documents' });
  }
}

module.exports = Client;
