const express = require('express');
const router = express.Router();
const clientController = require('../controllers/client.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Obter todos os clientes
router.get('/', clientController.getClients);

// Criar um novo cliente
router.post('/', clientController.createClient);

// Obter um cliente pelo ID
router.get('/:id', clientController.getClientById);

// Atualizar um cliente
router.put('/:id', clientController.updateClient);

// Excluir um cliente
router.delete('/:id', clientController.deleteClient);

module.exports = router;
