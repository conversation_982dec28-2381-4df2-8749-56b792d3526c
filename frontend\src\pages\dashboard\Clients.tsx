import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar,
  InputAdornment,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

import clientService, { Client, ClientFilter, CreateClientData, UpdateClientData } from '../../services/clientService';
import { useAuth } from '../../contexts/AuthContext';

const Clients: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filters, setFilters] = useState<ClientFilter>({
    search: '',
    documentType: undefined,
    isActive: undefined,
    page: 1,
    limit: 10
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [totalClients, setTotalClients] = useState(0);
  const [formData, setFormData] = useState<CreateClientData | UpdateClientData>({
    name: '',
    documentType: 'cpf',
    documentNumber: '',
    email: '',
    phone: '',
    address: {
      street: '',
      number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      zipCode: ''
    },
    documentTypes: []
  });

  useEffect(() => {
    fetchClients();
  }, [filters.page, filters.limit]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await clientService.getClients(filters);
      setClients(response.clients);
      setTotalClients(response.total);
    } catch (err) {
      console.error('Erro ao carregar clientes:', err);
      setError('Não foi possível carregar os clientes. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleDocumentTypeFilterChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    setFilters(prev => ({ ...prev, documentType: e.target.value as 'cpf' | 'cnpj' | undefined }));
  };

  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 }));
    fetchClients();
  };

  const resetFilters = () => {
    setFilters({
      search: '',
      documentType: undefined,
      isActive: undefined,
      page: 1,
      limit: 10
    });
    fetchClients();
  };

  const handleCreateClient = async () => {
    try {
      setLoading(true);
      setError(null);
      await clientService.createClient(formData as CreateClientData);
      setSuccess('Cliente criado com sucesso!');
      setOpenCreateDialog(false);
      resetForm();
      fetchClients();
    } catch (err: any) {
      console.error('Erro ao criar cliente:', err);
      setError(err.response?.data?.message || 'Não foi possível criar o cliente. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateClient = async () => {
    if (!selectedClient) return;

    try {
      setLoading(true);
      setError(null);
      await clientService.updateClient(selectedClient.id, formData);
      setSuccess('Cliente atualizado com sucesso!');
      setOpenEditDialog(false);
      fetchClients();
    } catch (err: any) {
      console.error('Erro ao atualizar cliente:', err);
      setError(err.response?.data?.message || 'Não foi possível atualizar o cliente. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async () => {
    if (!selectedClient) return;

    try {
      setLoading(true);
      setError(null);
      await clientService.deleteClient(selectedClient.id);
      setSuccess('Cliente excluído com sucesso!');
      setOpenDeleteDialog(false);
      setSelectedClient(null);
      fetchClients();
    } catch (err: any) {
      console.error('Erro ao excluir cliente:', err);
      setError(err.response?.data?.message || 'Não foi possível excluir o cliente. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleClientStatus = async (client: Client) => {
    try {
      setLoading(true);
      setError(null);
      await clientService.toggleClientStatus(client.id, !client.isActive);
      setSuccess(`Cliente ${client.isActive ? 'desativado' : 'ativado'} com sucesso!`);
      fetchClients();
    } catch (err: any) {
      console.error('Erro ao alterar status do cliente:', err);
      setError(err.response?.data?.message || 'Não foi possível alterar o status do cliente. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      documentType: 'cpf',
      documentNumber: '',
      email: '',
      phone: '',
      address: {
        street: '',
        number: '',
        complement: '',
        neighborhood: '',
        city: '',
        state: '',
        zipCode: ''
      },
      documentTypes: []
    });
  };

  const handleOpenEditDialog = (client: Client) => {
    setSelectedClient(client);
    setFormData({
      name: client.name,
      email: client.email,
      phone: client.phone || '',
      address: client.address || {
        street: '',
        number: '',
        complement: '',
        neighborhood: '',
        city: '',
        state: '',
        zipCode: ''
      },
      documentTypes: client.documentTypes
    });
    setOpenEditDialog(true);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  const formatDocumentNumber = (type: string, number: string) => {
    if (type === 'cpf') {
      return number.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    } else {
      return number.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Clientes
      </Typography>

      <Paper sx={{ width: '100%', mb: 3, p: 2 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Button 
              variant="outlined" 
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{ mr: 1 }}
            >
              {showFilters ? 'Ocultar Filtros' : 'Mostrar Filtros'}
            </Button>
            
            <Button 
              variant="outlined" 
              startIcon={<RefreshIcon />}
              onClick={fetchClients}
              disabled={loading}
            >
              Atualizar
            </Button>
          </Box>
          
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            onClick={() => {
              resetForm();
              setOpenCreateDialog(true);
            }}
          >
            Novo Cliente
          </Button>
        </Box>
        
        {showFilters && (
          <Box sx={{ mb: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  label="Buscar"
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                  placeholder="Nome, email ou documento"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Tipo de Documento</InputLabel>
                  <Select
                    value={filters.documentType || ''}
                    onChange={handleDocumentTypeFilterChange}
                    label="Tipo de Documento"
                    name="documentType"
                  >
                    <MenuItem value="">Todos</MenuItem>
                    <MenuItem value="cpf">CPF</MenuItem>
                    <MenuItem value="cnpj">CNPJ</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={applyFilters}
                    disabled={loading}
                  >
                    Aplicar Filtros
                  </Button>
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={resetFilters}
                    disabled={loading}
                  >
                    Limpar
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : clients.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            Nenhum cliente encontrado. Clique em "Novo Cliente" para adicionar.
          </Alert>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Nome</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Documento</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Email</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Status</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Criado em</th>
                  <th style={{ padding: '12px', textAlign: 'center' }}>Ações</th>
                </tr>
              </thead>
              <tbody>
                {clients.map((client) => (
                  <tr key={client.id} style={{ borderBottom: '1px solid #eee' }}>
                    <td style={{ padding: '12px' }}>{client.name}</td>
                    <td style={{ padding: '12px' }}>
                      {formatDocumentNumber(client.documentType, client.documentNumber)}
                      <Typography variant="caption" display="block" color="textSecondary">
                        {client.documentType.toUpperCase()}
                      </Typography>
                    </td>
                    <td style={{ padding: '12px' }}>{client.email}</td>
                    <td style={{ padding: '12px' }}>
                      <Chip 
                        label={client.isActive ? 'Ativo' : 'Inativo'}
                        color={client.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </td>
                    <td style={{ padding: '12px' }}>{formatDate(client.createdAt)}</td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <Tooltip title="Editar">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenEditDialog(client)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Excluir">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedClient(client);
                            setOpenDeleteDialog(true);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        )}
      </Paper>

      {/* Dialog de criação */}
      <Dialog
        open={openCreateDialog}
        onClose={() => setOpenCreateDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Novo Cliente</DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome"
                name="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Tipo de Documento</InputLabel>
                <Select
                  value={(formData as CreateClientData).documentType}
                  onChange={(e) => setFormData({ ...formData, documentType: e.target.value as 'cpf' | 'cnpj' })}
                  label="Tipo de Documento"
                >
                  <MenuItem value="cpf">CPF</MenuItem>
                  <MenuItem value="cnpj">CNPJ</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Número do Documento"
                name="documentNumber"
                value={(formData as CreateClientData).documentNumber}
                onChange={(e) => setFormData({ ...formData, documentNumber: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Telefone"
                name="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Endereço
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                fullWidth
                label="Rua"
                name="street"
                value={formData.address?.street}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, street: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Número"
                name="number"
                value={formData.address?.number}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, number: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Complemento"
                name="complement"
                value={formData.address?.complement}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, complement: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bairro"
                name="neighborhood"
                value={formData.address?.neighborhood}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, neighborhood: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Cidade"
                name="city"
                value={formData.address?.city}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, city: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Estado"
                name="state"
                value={formData.address?.state}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, state: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="CEP"
                name="zipCode"
                value={formData.address?.zipCode}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, zipCode: e.target.value }
                })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCreateDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleCreateClient}
            color="primary"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Criar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de edição */}
      <Dialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Editar Cliente</DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome"
                name="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Telefone"
                name="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Endereço
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            <Grid item xs={12} sm={9}>
              <TextField
                fullWidth
                label="Rua"
                name="street"
                value={formData.address?.street}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, street: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Número"
                name="number"
                value={formData.address?.number}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, number: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Complemento"
                name="complement"
                value={formData.address?.complement}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, complement: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bairro"
                name="neighborhood"
                value={formData.address?.neighborhood}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, neighborhood: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Cidade"
                name="city"
                value={formData.address?.city}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, city: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Estado"
                name="state"
                value={formData.address?.state}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, state: e.target.value }
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="CEP"
                name="zipCode"
                value={formData.address?.zipCode}
                onChange={(e) => setFormData({
                  ...formData,
                  address: { ...formData.address!, zipCode: e.target.value }
                })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleUpdateClient}
            color="primary"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de confirmação de exclusão */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o cliente "{selectedClient?.name}"?
            Esta ação não pode ser desfeita.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleDeleteClient}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Excluir'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar de sucesso/erro */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Clients; 