import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Divider,
  Button,
  TextField,
  MenuItem,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  PlayArrow as ProcessIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

import FileUpload from '../../components/FileUpload';
import documentService, { Document, DocumentFilter } from '../../services/documentService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`documents-tabpanel-${index}`}
      aria-labelledby={`documents-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `documents-tab-${index}`,
    'aria-controls': `documents-tabpanel-${index}`,
  };
}

const Documents: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filters, setFilters] = useState<DocumentFilter>({
    status: '',
    type: '',
    startDate: '',
    endDate: '',
    page: 1,
    limit: 10
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [totalDocuments, setTotalDocuments] = useState(0);

  // Carregar documentos ao montar o componente
  useEffect(() => {
    fetchDocuments();
  }, [filters.page, filters.limit]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await documentService.getDocuments(filters);
      setDocuments(response.documents);
      setTotalDocuments(response.total);
    } catch (err) {
      console.error('Erro ao carregar documentos:', err);
      setError('Não foi possível carregar os documentos. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 })); // Resetar para a primeira página
    fetchDocuments();
  };

  const resetFilters = () => {
    setFilters({
      status: '',
      type: '',
      startDate: '',
      endDate: '',
      page: 1,
      limit: 10
    });
    fetchDocuments();
  };

  const handleUpload = async (files: File[]) => {
    try {
      setLoading(true);
      setError(null);

      // Verificar se há arquivos para upload
      if (!files || files.length === 0) {
        setError('Nenhum arquivo selecionado para upload');
        return;
      }

      // Fazer o upload dos documentos
      const response = await documentService.uploadDocuments(files);

      // Verificar se o upload foi bem-sucedido
      if (response.success) {
        setSuccess(response.message || `${response.count} documento(s) enviado(s) com sucesso!`);
        fetchDocuments(); // Recarregar a lista após o upload
      } else {
        setError('Erro ao fazer upload dos documentos');
      }
    } catch (err: any) {
      console.error('Erro ao fazer upload:', err);
      setError(err.message || 'Não foi possível fazer o upload dos documentos. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDocument = async () => {
    if (!selectedDocument) return;

    try {
      setLoading(true);
      setError(null);
      await documentService.deleteDocument(selectedDocument.id);
      setSuccess('Documento excluído com sucesso!');
      setOpenDeleteDialog(false);
      setSelectedDocument(null);
      fetchDocuments(); // Recarregar a lista após a exclusão
    } catch (err) {
      console.error('Erro ao excluir documento:', err);
      setError('Não foi possível excluir o documento. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadDocument = async (document: Document) => {
    try {
      setLoading(true);
      setError(null);
      const blob = await documentService.downloadDocument(document.id);

      // Criar URL para o blob e iniciar o download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = document.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess('Download iniciado com sucesso!');
    } catch (err) {
      console.error('Erro ao fazer download:', err);
      setError('Não foi possível fazer o download do documento. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleProcessDocument = async (document: Document) => {
    try {
      setLoading(true);
      setError(null);
      await documentService.processDocument(document.id);
      setSuccess('Documento enviado para processamento!');
      fetchDocuments(); // Recarregar a lista após o processamento
    } catch (err) {
      console.error('Erro ao processar documento:', err);
      setError('Não foi possível processar o documento. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'processed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'processing':
        return 'Processando';
      case 'processed':
        return 'Processado';
      case 'error':
        return 'Erro';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Gerenciamento de Documentos
      </Typography>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Todos os Documentos" {...a11yProps(0)} />
          <Tab label="Upload de Documentos" {...a11yProps(1)} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
            >
              {showFilters ? 'Ocultar Filtros' : 'Mostrar Filtros'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchDocuments}
              disabled={loading}
            >
              Atualizar
            </Button>
          </Box>

          {showFilters && (
            <Paper sx={{ p: 2, mb: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    select
                    fullWidth
                    label="Status"
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="">Todos</MenuItem>
                    <MenuItem value="pending">Pendente</MenuItem>
                    <MenuItem value="processing">Processando</MenuItem>
                    <MenuItem value="processed">Processado</MenuItem>
                    <MenuItem value="error">Erro</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    select
                    fullWidth
                    label="Tipo"
                    name="type"
                    value={filters.type}
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="">Todos</MenuItem>
                    <MenuItem value="pdf">PDF</MenuItem>
                    <MenuItem value="image">Imagem</MenuItem>
                    <MenuItem value="excel">Excel</MenuItem>
                    <MenuItem value="word">Word</MenuItem>
                    <MenuItem value="other">Outros</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    label="Data Inicial"
                    name="startDate"
                    type="date"
                    value={filters.startDate}
                    onChange={handleFilterChange}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    label="Data Final"
                    name="endDate"
                    type="date"
                    value={filters.endDate}
                    onChange={handleFilterChange}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                  <Button variant="outlined" onClick={resetFilters}>
                    Limpar
                  </Button>
                  <Button variant="contained" startIcon={<SearchIcon />} onClick={applyFilters}>
                    Aplicar Filtros
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : documents.length === 0 ? (
            <Alert severity="info" sx={{ mt: 2 }}>
              Nenhum documento encontrado. Faça upload de novos documentos na aba "Upload de Documentos".
            </Alert>
          ) : (
            <Box sx={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ backgroundColor: '#f5f5f5' }}>
                    <th style={{ padding: '12px', textAlign: 'left' }}>Nome</th>
                    <th style={{ padding: '12px', textAlign: 'left' }}>Tipo</th>
                    <th style={{ padding: '12px', textAlign: 'left' }}>Tamanho</th>
                    <th style={{ padding: '12px', textAlign: 'left' }}>Status</th>
                    <th style={{ padding: '12px', textAlign: 'left' }}>Data de Envio</th>
                    <th style={{ padding: '12px', textAlign: 'center' }}>Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((doc) => (
                    <tr key={doc.id} style={{ borderBottom: '1px solid #eee' }}>
                      <td style={{ padding: '12px' }}>{doc.name}</td>
                      <td style={{ padding: '12px' }}>{doc.type}</td>
                      <td style={{ padding: '12px' }}>{formatFileSize(doc.size)}</td>
                      <td style={{ padding: '12px' }}>
                        <Chip
                          label={getStatusLabel(doc.status)}
                          color={getStatusColor(doc.status) as any}
                          size="small"
                        />
                      </td>
                      <td style={{ padding: '12px' }}>{formatDate(doc.createdAt)}</td>
                      <td style={{ padding: '12px', textAlign: 'center' }}>
                        <Tooltip title="Visualizar">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedDocument(doc);
                              setOpenViewDialog(true);
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download">
                          <IconButton
                            size="small"
                            onClick={() => handleDownloadDocument(doc)}
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                        {doc.status !== 'processing' && (
                          <Tooltip title="Processar">
                            <IconButton
                              size="small"
                              onClick={() => handleProcessDocument(doc)}
                              disabled={doc.status === 'processed'}
                            >
                              <ProcessIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Excluir">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => {
                              setSelectedDocument(doc);
                              setOpenDeleteDialog(true);
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <FileUpload
            onUpload={handleUpload}
            acceptedFileTypes=".pdf,.jpg,.jpeg,.png,.xls,.xlsx,.doc,.docx"
            maxFiles={5}
            maxFileSize={20 * 1024 * 1024} // 20MB
            title="Upload de Documentos Contábeis"
            description="Arraste e solte seus documentos contábeis aqui ou clique para selecionar"
          />

          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Instruções para Upload
            </Typography>
            <Typography variant="body2" paragraph>
              • Formatos aceitos: PDF, imagens (JPG, PNG), planilhas (XLS, XLSX) e documentos Word (DOC, DOCX).
            </Typography>
            <Typography variant="body2" paragraph>
              • Tamanho máximo por arquivo: 20MB.
            </Typography>
            <Typography variant="body2" paragraph>
              • Máximo de 5 arquivos por upload.
            </Typography>
            <Typography variant="body2" paragraph>
              • Para extratos bancários, certifique-se de que o documento esteja legível e contenha todas as informações necessárias.
            </Typography>
            <Typography variant="body2" paragraph>
              • Após o upload, os documentos serão analisados e processados automaticamente.
            </Typography>
          </Box>
        </TabPanel>
      </Paper>

      {/* Dialog de confirmação de exclusão */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o documento "{selectedDocument?.name}"?
            Esta ação não pode ser desfeita.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleDeleteDocument}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Excluir'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de visualização de documento */}
      <Dialog
        open={openViewDialog}
        onClose={() => setOpenViewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedDocument?.name}
          <IconButton
            aria-label="close"
            onClick={() => setOpenViewDialog(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            &times;
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedDocument && (
            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Nome:</Typography>
                  <Typography variant="body1">{selectedDocument.name}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Tipo:</Typography>
                  <Typography variant="body1">{selectedDocument.type}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Tamanho:</Typography>
                  <Typography variant="body1">{formatFileSize(selectedDocument.size)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Status:</Typography>
                  <Chip
                    label={getStatusLabel(selectedDocument.status)}
                    color={getStatusColor(selectedDocument.status) as any}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Data de Envio:</Typography>
                  <Typography variant="body1">{formatDate(selectedDocument.createdAt)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Última Atualização:</Typography>
                  <Typography variant="body1">{formatDate(selectedDocument.updatedAt)}</Typography>
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2">Visualização:</Typography>

                  {selectedDocument.type.includes('image') ? (
                    <Box sx={{ mt: 2, textAlign: 'center' }}>
                      <img
                        src={selectedDocument.url}
                        alt={selectedDocument.name}
                        style={{ maxWidth: '100%', maxHeight: '500px' }}
                      />
                    </Box>
                  ) : selectedDocument.type.includes('pdf') ? (
                    <Box sx={{ mt: 2, height: '500px' }}>
                      <iframe
                        src={selectedDocument.url}
                        width="100%"
                        height="100%"
                        title={selectedDocument.name}
                      />
                    </Box>
                  ) : (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      Visualização não disponível para este tipo de arquivo. Faça o download para visualizar.
                    </Alert>
                  )}
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => handleDownloadDocument(selectedDocument!)}
            startIcon={<DownloadIcon />}
            disabled={loading}
          >
            Download
          </Button>
          <Button
            onClick={() => setOpenViewDialog(false)}
          >
            Fechar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Documents;