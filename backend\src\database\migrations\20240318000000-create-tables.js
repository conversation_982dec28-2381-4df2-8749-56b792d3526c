module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Criar tabela de planos
    await queryInterface.createTable('plans', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      additional_client_price: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0
      },
      max_users: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      max_clients: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      max_documents_per_month: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      max_transactions_per_month: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      storage_limit: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      features: {
        type: Sequelize.JSON,
        allowNull: true
      },
      plan_type: {
        type: Sequelize.ENUM('individual', 'business_basic', 'business_plus', 'business_premium'),
        allowNull: false
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Criar tabela de empresas
    await queryInterface.createTable('companies', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      document_type: {
        type: Sequelize.ENUM('cnpj', 'cpf'),
        allowNull: false
      },
      document_number: {
        type: Sequelize.STRING(14),
        allowNull: false,
        unique: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      address_street: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address_number: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      address_complement: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address_neighborhood: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address_city: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address_state: {
        type: Sequelize.CHAR(2),
        allowNull: true
      },
      address_zip_code: {
        type: Sequelize.STRING(8),
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Criar tabela de usuários
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false
      },
      role: {
        type: Sequelize.ENUM('admin', 'manager', 'accountant', 'client'),
        allowNull: false
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      plan_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'plans',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      last_login: {
        type: Sequelize.DATE,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Criar tabela de documentos
    await queryInterface.createTable('documents', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      file_path: {
        type: Sequelize.STRING,
        allowNull: false
      },
      file_size: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      file_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('pending', 'processing', 'processed', 'approved', 'rejected', 'error'),
        defaultValue: 'pending',
        allowNull: false
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true
      },
      processing_result: {
        type: Sequelize.JSON,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Criar tabela de regras contábeis
    await queryInterface.createTable('accounting_rules', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      rule_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      conditions: {
        type: Sequelize.JSON,
        allowNull: false
      },
      actions: {
        type: Sequelize.JSON,
        allowNull: false
      },
      company_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'companies',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('accounting_rules');
    await queryInterface.dropTable('documents');
    await queryInterface.dropTable('users');
    await queryInterface.dropTable('companies');
    await queryInterface.dropTable('plans');
  }
}; 