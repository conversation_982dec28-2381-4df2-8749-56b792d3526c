import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  TextField,
  Button,
  Paper,
  Snackbar,
  Alert,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  WhatsApp as WhatsAppIcon,
} from '@mui/icons-material';

const Contact: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [message, setMessage] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validação básica
    if (!name.trim() || !email.trim() || !message.trim()) {
      setSnackbar({
        open: true,
        message: 'Por favor, preencha todos os campos obrigatórios.',
        severity: 'error',
      });
      return;
    }
    
    // Simulação de envio do formulário
    // Em um ambiente real, aqui seria feita uma chamada à API
    console.log({ name, email, phone, message });
    
    // Limpar formulário e mostrar mensagem de sucesso
    setName('');
    setEmail('');
    setPhone('');
    setMessage('');
    setSnackbar({
      open: true,
      message: 'Mensagem enviada com sucesso! Entraremos em contato em breve.',
      severity: 'success',
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const contactInfo = [
    {
      icon: <PhoneIcon fontSize="large" color="primary" />,
      title: 'Telefone',
      content: '(11) 9999-9999',
    },
    {
      icon: <WhatsAppIcon fontSize="large" color="primary" />,
      title: 'WhatsApp',
      content: '(11) 9999-9999',
    },
    {
      icon: <EmailIcon fontSize="large" color="primary" />,
      title: 'E-mail',
      content: '<EMAIL>',
    },
    {
      icon: <LocationIcon fontSize="large" color="primary" />,
      title: 'Endereço',
      content: 'Av. Paulista, 1000 - Bela Vista, São Paulo - SP',
    },
  ];

  return (
    <Box>
      {/* Seção de Cabeçalho */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h1" gutterBottom>
            Entre em Contato
          </Typography>
          <Typography variant="h6" sx={{ opacity: 0.8 }}>
            Estamos aqui para ajudar. Entre em contato conosco!
          </Typography>
        </Container>
      </Box>

      {/* Seção de Informações de Contato */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={5}>
            <Typography variant="h4" component="h2" gutterBottom>
              Informações de Contato
            </Typography>
            <Typography variant="body1" paragraph color="text.secondary">
              Temos uma equipe dedicada pronta para responder suas dúvidas e ajudar com qualquer problema.
            </Typography>
            
            <Box sx={{ mt: 4 }}>
              <Grid container spacing={3}>
                {contactInfo.map((info, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Card sx={{ height: '100%' }}>
                      <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                        <Box sx={{ mb: 2 }}>{info.icon}</Box>
                        <Typography variant="h6" gutterBottom>
                          {info.title}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {info.content}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Grid>

          <Grid item xs={12} md={7}>
            <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
              <Typography variant="h4" component="h2" gutterBottom>
                Envie uma Mensagem
              </Typography>
              <Typography variant="body1" paragraph color="text.secondary">
                Preencha o formulário abaixo e entraremos em contato o mais breve possível.
              </Typography>
              
              <Divider sx={{ my: 3 }} />
              
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      id="name"
                      label="Nome Completo"
                      name="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      id="email"
                      label="E-mail"
                      name="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="phone"
                      label="Telefone"
                      name="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      required
                      fullWidth
                      id="message"
                      label="Mensagem"
                      name="message"
                      multiline
                      rows={4}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                    />
                  </Grid>
                </Grid>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  sx={{ mt: 3, py: 1.5, px: 4 }}
                >
                  Enviar Mensagem
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Mapa ou Localização */}
      <Box sx={{ bgcolor: 'grey.100', py: 6 }}>
        <Container maxWidth="lg">
          <Typography variant="h4" component="h2" gutterBottom align="center">
            Nossa Localização
          </Typography>
          <Typography variant="body1" paragraph align="center" color="text.secondary" sx={{ mb: 4 }}>
            Estamos localizados no coração de São Paulo, com fácil acesso.
          </Typography>
          
          {/* Aqui seria inserido um componente de mapa, como Google Maps */}
          <Paper
            sx={{
              width: '100%',
              height: 400,
              bgcolor: 'grey.300',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography variant="body1" color="text.secondary">
              Mapa será carregado aqui
            </Typography>
          </Paper>
        </Container>
      </Box>

      {/* Snackbar para feedback */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Contact; 