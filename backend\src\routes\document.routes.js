const express = require('express');
const router = express.Router();
const documentController = require('../controllers/document.controller');
const { authenticate } = require('../middlewares/auth.middleware');
const upload = require('../middlewares/upload');

// Rotas protegidas (requerem autenticação)
router.use(authenticate);

// Rota para upload de documentos
router.post('/upload', upload.array('files', 10), documentController.uploadDocuments);

// Rota para listar documentos
router.get('/', documentController.getDocuments);

// Rota para obter um documento pelo ID
router.get('/:id', documentController.getDocumentById);

// Rota para atualizar o status de um documento
router.patch('/:id/status', documentController.updateDocumentStatus);

// Rota para excluir um documento
router.delete('/:id', documentController.deleteDocument);

// Rota para download de um documento
router.get('/:id/download', documentController.downloadDocument);

// Rota para processar um documento
router.post('/:id/process', documentController.processDocument);

module.exports = router;