import React from 'react';
import { Outlet, Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Link,
  useTheme,
  useMediaQuery,
} from '@mui/material';

const AuthLayout: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: 'grey.100',
      }}
    >
      <Box
        component="header"
        sx={{
          py: 2,
          px: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography
          variant="h6"
          component={RouterLink}
          to="/"
          sx={{
            fontWeight: 700,
            color: 'primary.main',
            textDecoration: 'none',
          }}
        >
          Nexlify Contábil
        </Typography>
      </Box>

      <Container
        component="main"
        maxWidth="sm"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          flexGrow: 1,
          py: 4,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: { xs: 3, md: 5 },
            borderRadius: 2,
          }}
        >
          <Outlet />
        </Paper>
      </Container>

      <Box
        component="footer"
        sx={{
          py: 3,
          px: 2,
          mt: 'auto',
          textAlign: 'center',
        }}
      >
        <Typography variant="body2" color="text.secondary">
          © {new Date().getFullYear()} Nexlify Contábil. Todos os direitos reservados.
        </Typography>
        <Box sx={{ mt: 1 }}>
          <Link component={RouterLink} to="/" color="inherit" sx={{ mx: 1 }}>
            Início
          </Link>
          <Link component={RouterLink} to="/planos" color="inherit" sx={{ mx: 1 }}>
            Planos
          </Link>
          <Link href="mailto:<EMAIL>" color="inherit" sx={{ mx: 1 }}>
            Suporte
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default AuthLayout; 