const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { createError } = require('../utils/error');

// Diretório para armazenar os arquivos
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// Garantir que o diretório de uploads exista
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// Configuração de armazenamento
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    try {
      // Verificar se o usuário está autenticado
      if (!req.user || !req.user.id) {
        return cb(new Error('Usuário não autenticado'), null);
      }

      // Criar diretório para o usuário se não existir
      const userDir = path.join(UPLOAD_DIR, req.user.id.toString());
      if (!fs.existsSync(userDir)) {
        fs.mkdirSync(userDir, { recursive: true });
      }

      // Criar diretório para a data atual se não existir
      const today = new Date();
      const dateDir = path.join(
        userDir,
        `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`
      );
      if (!fs.existsSync(dateDir)) {
        fs.mkdirSync(dateDir, { recursive: true });
      }

      cb(null, dateDir);
    } catch (error) {
      console.error('Erro ao configurar destino do upload:', error);
      cb(error, null);
    }
  },
  filename: (req, file, cb) => {
    try {
      // Gerar nome de arquivo único
      const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
      const ext = path.extname(file.originalname);

      // Sanitizar o nome do arquivo original para evitar caracteres problemáticos
      const sanitizedName = file.originalname
        .replace(/[^a-zA-Z0-9.-]/g, '_') // Substituir caracteres especiais por underscore
        .replace(/\s+/g, '_');           // Substituir espaços por underscore

      cb(null, `${sanitizedName.split('.')[0]}-${uniqueSuffix}${ext}`);
    } catch (error) {
      console.error('Erro ao gerar nome de arquivo:', error);
      cb(error, null);
    }
  }
});

// Filtro de arquivos
const fileFilter = (req, file, cb) => {
  // Tipos de arquivo permitidos
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/jpg',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/csv'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(createError(400, `Tipo de arquivo não permitido: ${file.mimetype}`), false);
  }
};

// Limites
const limits = {
  fileSize: 20 * 1024 * 1024, // 20MB
  files: 10
};

// Configuração do multer
const upload = multer({
  storage,
  fileFilter,
  limits
});

module.exports = upload;