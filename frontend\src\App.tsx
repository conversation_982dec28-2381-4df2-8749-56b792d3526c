import React from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';

// Layouts
import MainLayout from './components/layouts/MainLayout';
import AuthLayout from './components/layouts/AuthLayout';

// Páginas públicas
import Home from './pages/public/Home';
import PlanSelection from './pages/public/PlanSelection';
import About from './pages/public/About';
import Contact from './pages/public/Contact';
import Register from './pages/public/Register';
import RegisterSuccess from './pages/public/RegisterSuccess';
import ForgotPassword from './pages/public/ForgotPassword';
import ResetPassword from './pages/public/ResetPassword';
import NotFound from './pages/public/NotFound';

// Páginas de autenticação
import Login from './pages/auth/Login';

// Páginas protegidas
import Dashboard from './pages/dashboard/Dashboard';
import Documents from './pages/dashboard/Documents';
import Users from './pages/dashboard/Users';
import AccountingRules from './pages/dashboard/AccountingRules';
import Clients from './pages/dashboard/Clients';

// Componente para rotas protegidas
interface PrivateRouteProps {
  children: React.ReactNode;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  console.log('PrivateRoute:', { isAuthenticated, loading, pathname: location.pathname });

  if (loading) {
    return <div>Carregando...</div>;
  }

  if (!isAuthenticated) {
    console.log('Usuário não autenticado, redirecionando para login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  console.log('Usuário autenticado, renderizando rota protegida');
  return <>{children}</>;
};

const App: React.FC = () => {
  return (
    <Routes>
      {/* Rotas públicas */}
      <Route path="/" element={<MainLayout />}>
        <Route index element={<Home />} />
        <Route path="planos" element={<PlanSelection />} />
        <Route path="sobre" element={<About />} />
        <Route path="contato" element={<Contact />} />
      </Route>

      {/* Rotas de autenticação */}
      <Route path="/" element={<AuthLayout />}>
        <Route path="login" element={<Login />} />
        <Route path="registro" element={<Register />} />
        <Route path="registro-sucesso" element={<RegisterSuccess />} />
        <Route path="esqueci-senha" element={<ForgotPassword />} />
        <Route path="redefinir-senha/:token" element={<ResetPassword />} />
      </Route>

      {/* Todas as rotas protegidas agrupadas */}
      <Route element={
        <PrivateRoute>
          <MainLayout />
        </PrivateRoute>
      }>
        {/* Dashboard e suas subrotas */}
        <Route path="/dashboard">
          <Route index element={<Dashboard />} />
          <Route path="documentos" element={<Documents />} />
          <Route path="usuarios" element={<Users />} />
          <Route path="regras-contabeis" element={<AccountingRules />} />
        </Route>

        {/* Outras rotas protegidas */}
        <Route path="/clientes" element={<Clients />} />
      </Route>

      {/* Rota de fallback */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default App; 