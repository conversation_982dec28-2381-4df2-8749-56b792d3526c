const { Plan } = require('../models');

/**
 * Obter planos públicos
 * @route GET /api/plans/public
 * @access Public
 */
exports.getPublicPlans = async (req, res) => {
  try {
    const plans = await Plan.findAll({
      where: { is_active: true },
      order: [['price', 'ASC']]
    });
    
    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Erro ao obter planos públicos:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao obter planos públicos', 
      error: error.message 
    });
  }
};

/**
 * Obter todos os planos
 * @route GET /api/plans
 * @access Public
 */
exports.getPlans = async (req, res) => {
  try {
    // Filtros
    const where = { is_active: true };
    
    // Filtrar por tipo de plano (se especificado)
    if (req.query.plan_type) {
      where.plan_type = req.query.plan_type;
    }
    
    // Buscar planos
    const plans = await Plan.findAll({
      where,
      order: [['price', 'ASC']]
    });
    
    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Erro ao obter planos:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao obter planos', 
      error: error.message 
    });
  }
};

/**
 * Obter um plano pelo ID
 * @route GET /api/plans/:id
 * @access Public
 */
exports.getPlanById = async (req, res) => {
  try {
    const plan = await Plan.findByPk(req.params.id);
    
    if (!plan) {
      return res.status(404).json({ 
        success: false,
        message: 'Plano não encontrado' 
      });
    }
    
    res.json({
      success: true,
      data: plan
    });
  } catch (error) {
    console.error('Erro ao obter plano:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao obter plano', 
      error: error.message 
    });
  }
};

/**
 * Criar um novo plano
 * @route POST /api/plans
 * @access Private/Admin
 */
exports.createPlan = async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      additional_client_price,
      max_users,
      max_clients,
      max_documents_per_month,
      max_transactions_per_month,
      storage_limit,
      features,
      plan_type
    } = req.body;
    
    // Verificar se já existe um plano com o mesmo nome
    const existingPlan = await Plan.findOne({ where: { name } });
    if (existingPlan) {
      return res.status(400).json({ 
        success: false,
        message: 'Já existe um plano com este nome' 
      });
    }
    
    // Criar um novo plano
    const plan = await Plan.create({
      name,
      description,
      price,
      additional_client_price,
      max_users,
      max_clients,
      max_documents_per_month,
      max_transactions_per_month,
      storage_limit,
      features,
      plan_type
    });
    
    res.status(201).json({
      success: true,
      data: plan
    });
  } catch (error) {
    console.error('Erro ao criar plano:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao criar plano', 
      error: error.message 
    });
  }
};

/**
 * Atualizar um plano
 * @route PUT /api/plans/:id
 * @access Private/Admin
 */
exports.updatePlan = async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      additional_client_price,
      max_users,
      max_clients,
      max_documents_per_month,
      max_transactions_per_month,
      storage_limit,
      features,
      is_active,
      plan_type
    } = req.body;
    
    // Buscar o plano
    const plan = await Plan.findByPk(req.params.id);
    if (!plan) {
      return res.status(404).json({ 
        success: false,
        message: 'Plano não encontrado' 
      });
    }
    
    // Verificar se já existe outro plano com o mesmo nome
    if (name && name !== plan.name) {
      const existingPlan = await Plan.findOne({ where: { name } });
      if (existingPlan) {
        return res.status(400).json({ 
          success: false,
          message: 'Já existe um plano com este nome' 
        });
      }
    }
    
    // Atualizar os campos
    await plan.update({
      name: name || plan.name,
      description: description || plan.description,
      price: price !== undefined ? price : plan.price,
      additional_client_price: additional_client_price !== undefined ? additional_client_price : plan.additional_client_price,
      max_users: max_users !== undefined ? max_users : plan.max_users,
      max_clients: max_clients !== undefined ? max_clients : plan.max_clients,
      max_documents_per_month: max_documents_per_month !== undefined ? max_documents_per_month : plan.max_documents_per_month,
      max_transactions_per_month: max_transactions_per_month !== undefined ? max_transactions_per_month : plan.max_transactions_per_month,
      storage_limit: storage_limit !== undefined ? storage_limit : plan.storage_limit,
      features: features || plan.features,
      is_active: is_active !== undefined ? is_active : plan.is_active,
      plan_type: plan_type || plan.plan_type
    });
    
    res.json({
      success: true,
      data: plan
    });
  } catch (error) {
    console.error('Erro ao atualizar plano:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao atualizar plano', 
      error: error.message 
    });
  }
};

/**
 * Excluir um plano (soft delete)
 * @route DELETE /api/plans/:id
 * @access Private/Admin
 */
exports.deletePlan = async (req, res) => {
  try {
    // Buscar o plano
    const plan = await Plan.findByPk(req.params.id);
    if (!plan) {
      return res.status(404).json({ 
        success: false,
        message: 'Plano não encontrado' 
      });
    }
    
    // Desativar o plano (soft delete)
    await plan.update({ is_active: false });
    
    res.json({ 
      success: true,
      message: 'Plano desativado com sucesso' 
    });
  } catch (error) {
    console.error('Erro ao excluir plano:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao excluir plano', 
      error: error.message 
    });
  }
}; 