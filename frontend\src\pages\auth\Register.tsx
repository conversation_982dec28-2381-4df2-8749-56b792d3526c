import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';

interface Plan {
  id: string;
  name: string;
  price: number;
  description: string;
}

const Register: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);

  const { register, error, clearError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Obter o plano selecionado da URL, se houver
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const planId = params.get('planId');
    
    if (planId) {
      setSelectedPlan(planId);
      fetchPlanDetails(planId);
    }
  }, [location.search]);

  const fetchPlanDetails = async (planId: string) => {
    try {
      const response = await api.get(`/plans/${planId}`);
      setPlan(response.data);
    } catch (err) {
      console.error('Erro ao buscar detalhes do plano:', err);
    }
  };

  const handleNext = () => {
    if (activeStep === 0) {
      // Validar dados pessoais
      if (!validatePersonalInfo()) {
        return;
      }
    }
    
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validatePersonalInfo = () => {
    if (!name.trim()) {
      setFormError('Por favor, informe seu nome');
      return false;
    }
    
    if (!email.trim()) {
      setFormError('Por favor, informe seu e-mail');
      return false;
    }
    
    if (!password) {
      setFormError('Por favor, informe uma senha');
      return false;
    }
    
    if (password.length < 6) {
      setFormError('A senha deve ter pelo menos 6 caracteres');
      return false;
    }
    
    if (password !== confirmPassword) {
      setFormError('As senhas não coincidem');
      return false;
    }
    
    setFormError(null);
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePersonalInfo()) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      clearError();
      
      await register(name, email, password, selectedPlan || undefined);
      navigate('/dashboard');
    } catch (err: any) {
      setFormError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const steps = ['Informações Pessoais', 'Confirmação'];

  return (
    <Box>
      <Typography component="h1" variant="h5" align="center" gutterBottom>
        Criar Conta
      </Typography>
      
      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
        {plan 
          ? `Você está se registrando com o plano ${plan.name} por R$ ${plan.price.toFixed(2)}/mês`
          : 'Crie sua conta para começar a usar o Nexlify Contábil'}
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {(formError || error) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {formError || error}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit} noValidate>
        {activeStep === 0 ? (
          // Etapa 1: Informações Pessoais
          <>
            <TextField
              margin="normal"
              required
              fullWidth
              id="name"
              label="Nome Completo"
              name="name"
              autoComplete="name"
              autoFocus
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isSubmitting}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="E-mail"
              name="email"
              autoComplete="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isSubmitting}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Senha"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="new-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label="Confirmar Senha"
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              autoComplete="new-password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleToggleConfirmPasswordVisibility}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              fullWidth
              variant="contained"
              onClick={handleNext}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
              disabled={isSubmitting}
            >
              Próximo
            </Button>
          </>
        ) : (
          // Etapa 2: Confirmação
          <>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Confirme seus dados:
              </Typography>
              <Typography variant="body1">
                <strong>Nome:</strong> {name}
              </Typography>
              <Typography variant="body1">
                <strong>E-mail:</strong> {email}
              </Typography>
              {plan && (
                <Typography variant="body1">
                  <strong>Plano:</strong> {plan.name} - R$ {plan.price.toFixed(2)}/mês
                </Typography>
              )}
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button onClick={handleBack} disabled={isSubmitting}>
                Voltar
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isSubmitting}
                sx={{ py: 1.5, px: 4 }}
              >
                {isSubmitting ? <CircularProgress size={24} /> : 'Criar Conta'}
              </Button>
            </Box>
          </>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3 }}>
          <Typography variant="body2" sx={{ mr: 1 }}>
            Já tem uma conta?
          </Typography>
          <Link component={RouterLink} to="/login" variant="body2">
            Entrar
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default Register; 