import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';
import { BrowserRouter } from 'react-router-dom';

// Mock do AuthContext
jest.mock('./contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    isAuthenticated: false,
    loading: false,
    user: null,
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    error: null,
    clearError: jest.fn(),
    updateUser: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
  }),
}));

test('renderiza o componente App sem erros', () => {
  render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  );
  
  // Verifica se o componente foi renderizado
  expect(document.body).toBeInTheDocument();
}); 