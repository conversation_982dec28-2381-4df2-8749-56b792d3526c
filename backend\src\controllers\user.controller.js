const User = require('../models/User');
const Company = require('../models/Company');

/**
 * Obter todos os usuários
 * @route GET /api/users
 * @access Private/Admin
 */
exports.getUsers = async (req, res) => {
  try {
    // Filtros
    const filter = { isActive: true };
    
    // Se não for admin, filtrar por empresa
    if (req.user.role !== 'admin' && req.user.company) {
      filter.company = req.user.company;
    }
    
    // Paginação
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Buscar usuários
    const users = await User.find(filter)
      .select('-password')
      .populate('company', 'name')
      .populate('plan', 'name')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    // Contar total de usuários
    const total = await User.countDocuments(filter);
    
    res.json({
      users,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Erro ao obter usuários:', error);
    res.status(500).json({ message: 'Erro ao obter usuários', error: error.message });
  }
};

/**
 * Obter um usuário pelo ID
 * @route GET /api/users/:id
 * @access Private
 */
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-password')
      .populate('company')
      .populate('plan');
    
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Verificar permissão (admin ou mesmo usuário ou mesma empresa)
    if (
      req.user.role !== 'admin' && 
      req.user._id.toString() !== user._id.toString() && 
      (req.user.company && user.company && req.user.company.toString() !== user.company._id.toString())
    ) {
      return res.status(403).json({ message: 'Acesso não autorizado' });
    }
    
    res.json(user);
  } catch (error) {
    console.error('Erro ao obter usuário:', error);
    res.status(500).json({ message: 'Erro ao obter usuário', error: error.message });
  }
};

/**
 * Criar um novo usuário
 * @route POST /api/users
 * @access Private/Admin
 */
exports.createUser = async (req, res) => {
  try {
    const { name, email, password, role, company, plan } = req.body;
    
    // Verificar se o email já está em uso
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'Email já está em uso' });
    }
    
    // Criar um novo usuário
    const user = new User({
      name,
      email,
      password,
      role: role || 'client'
    });
    
    // Se for um usuário de empresa, associar à empresa
    if (company) {
      // Verificar se a empresa existe
      const companyExists = await Company.findById(company);
      if (!companyExists) {
        return res.status(404).json({ message: 'Empresa não encontrada' });
      }
      
      user.company = company;
      
      // Incrementar o contador de usuários da empresa
      await Company.findByIdAndUpdate(company, { $inc: { userCount: 1 } });
    }
    
    // Se for um usuário individual, associar ao plano
    if (plan) {
      user.plan = plan;
    }
    
    // Salvar o usuário no banco de dados
    await user.save();
    
    res.status(201).json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        company: user.company,
        plan: user.plan
      }
    });
  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    res.status(500).json({ message: 'Erro ao criar usuário', error: error.message });
  }
};

/**
 * Atualizar um usuário
 * @route PUT /api/users/:id
 * @access Private
 */
exports.updateUser = async (req, res) => {
  try {
    const { name, email, role, isActive, company, plan } = req.body;
    
    // Buscar o usuário
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Verificar permissão (admin ou mesmo usuário)
    if (req.user.role !== 'admin' && req.user._id.toString() !== user._id.toString()) {
      return res.status(403).json({ message: 'Acesso não autorizado' });
    }
    
    // Verificar se o email já está em uso (se estiver sendo alterado)
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({ message: 'Email já está em uso' });
      }
      user.email = email;
    }
    
    // Atualizar os campos
    if (name) user.name = name;
    
    // Apenas admin pode alterar role, status e empresa
    if (req.user.role === 'admin') {
      if (role) user.role = role;
      if (isActive !== undefined) user.isActive = isActive;
      
      // Se estiver alterando a empresa
      if (company && company !== user.company) {
        // Decrementar contador da empresa anterior
        if (user.company) {
          await Company.findByIdAndUpdate(user.company, { $inc: { userCount: -1 } });
        }
        
        // Incrementar contador da nova empresa
        await Company.findByIdAndUpdate(company, { $inc: { userCount: 1 } });
        
        user.company = company;
      }
      
      // Se estiver alterando o plano
      if (plan) {
        user.plan = plan;
      }
    }
    
    // Salvar as alterações
    await user.save();
    
    res.json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        company: user.company,
        plan: user.plan
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar usuário:', error);
    res.status(500).json({ message: 'Erro ao atualizar usuário', error: error.message });
  }
};

/**
 * Alterar senha do usuário
 * @route PUT /api/users/:id/change-password
 * @access Private
 */
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    // Buscar o usuário
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Verificar permissão (admin ou mesmo usuário)
    if (req.user.role !== 'admin' && req.user._id.toString() !== user._id.toString()) {
      return res.status(403).json({ message: 'Acesso não autorizado' });
    }
    
    // Se não for admin, verificar a senha atual
    if (req.user.role !== 'admin') {
      const isMatch = await user.comparePassword(currentPassword);
      if (!isMatch) {
        return res.status(401).json({ message: 'Senha atual incorreta' });
      }
    }
    
    // Definir a nova senha
    user.password = newPassword;
    
    // Salvar as alterações
    await user.save();
    
    res.json({ message: 'Senha alterada com sucesso' });
  } catch (error) {
    console.error('Erro ao alterar senha:', error);
    res.status(500).json({ message: 'Erro ao alterar senha', error: error.message });
  }
};

/**
 * Excluir um usuário (soft delete)
 * @route DELETE /api/users/:id
 * @access Private/Admin
 */
exports.deleteUser = async (req, res) => {
  try {
    // Buscar o usuário
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Desativar o usuário (soft delete)
    user.isActive = false;
    
    // Salvar as alterações
    await user.save();
    
    // Se o usuário pertencer a uma empresa, decrementar o contador
    if (user.company) {
      await Company.findByIdAndUpdate(user.company, { $inc: { userCount: -1 } });
    }
    
    res.json({ message: 'Usuário desativado com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir usuário:', error);
    res.status(500).json({ message: 'Erro ao excluir usuário', error: error.message });
  }
}; 