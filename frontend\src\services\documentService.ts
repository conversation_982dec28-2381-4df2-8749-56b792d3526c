import api from './api';

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  status: 'pending' | 'processing' | 'processed' | 'error';
  createdAt: string;
  updatedAt: string;
  userId: string;
  clientId?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface DocumentFilter {
  status?: string;
  type?: string;
  clientId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

const documentService = {
  /**
   * Upload de documentos
   * @param files Array de arquivos para upload
   * @returns Promise com os documentos criados
   */
  async uploadDocuments(files: File[]): Promise<{
    success: boolean;
    count: number;
    data: Document[];
    message: string;
  }> {
    if (!files || files.length === 0) {
      throw new Error('Nenhum arquivo selecionado para upload');
    }

    // Verificar tamanho dos arquivos (limite de 20MB por arquivo)
    const maxSize = 20 * 1024 * 1024; // 20MB em bytes
    for (const file of files) {
      if (file.size > maxSize) {
        throw new Error(`O arquivo ${file.name} excede o tamanho máximo permitido (20MB)`);
      }
    }

    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    try {
      const response = await api.post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Adicionar timeout para evitar que a requisição fique pendente indefinidamente
        timeout: 60000, // 60 segundos
      });

      return response.data;
    } catch (error: any) {
      console.error('Erro no upload de documentos:', error);

      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Por favor, faça login novamente.');
      } else if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('O tempo limite para upload foi excedido. Tente novamente com arquivos menores ou uma conexão mais rápida.');
      } else {
        throw new Error('Erro ao fazer upload dos documentos. Verifique sua conexão e tente novamente.');
      }
    }
  },

  /**
   * Lista documentos com filtros opcionais
   * @param filters Filtros para a listagem
   * @returns Promise com a lista de documentos
   */
  async getDocuments(filters?: DocumentFilter): Promise<{
    documents: Document[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const response = await api.get('/documents', { params: filters });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Por favor, faça login novamente.');
      }
      throw error;
    }
  },

  /**
   * Obtém um documento pelo ID
   * @param id ID do documento
   * @returns Promise com o documento
   */
  async getDocumentById(id: string): Promise<Document> {
    const response = await api.get(`/documents/${id}`);
    return response.data;
  },

  /**
   * Atualiza o status de um documento
   * @param id ID do documento
   * @param status Novo status
   * @returns Promise com o documento atualizado
   */
  async updateDocumentStatus(
    id: string,
    status: 'pending' | 'processing' | 'processed' | 'error'
  ): Promise<Document> {
    const response = await api.patch(`/documents/${id}/status`, { status });
    return response.data;
  },

  /**
   * Exclui um documento
   * @param id ID do documento
   */
  async deleteDocument(id: string): Promise<void> {
    try {
      await api.delete(`/documents/${id}`);
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Por favor, faça login novamente.');
      }
      throw error;
    }
  },

  /**
   * Download de um documento
   * @param id ID do documento
   * @returns Promise com o blob do arquivo
   */
  async downloadDocument(id: string): Promise<Blob> {
    try {
      const response = await api.get(`/documents/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Por favor, faça login novamente.');
      }
      throw error;
    }
  },

  /**
   * Processa um documento
   * @param id ID do documento
   * @returns Promise com o documento processado
   */
  async processDocument(id: string): Promise<Document> {
    try {
      const response = await api.post(`/documents/${id}/process`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Por favor, faça login novamente.');
      }
      throw error;
    }
  },
};

export default documentService;