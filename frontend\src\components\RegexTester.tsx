import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  Divider,
  Grid,
  Tooltip,
  IconButton,
  Chip
} from '@mui/material';
import {
  Help as HelpIcon,
  PlayArrow as TestIcon,
  ContentCopy as CopyIcon
} from '@mui/icons-material';

interface RegexTesterProps {
  initialPattern?: string;
  initialSampleText?: string;
  onPatternChange?: (pattern: string) => void;
  onTestResult?: (result: { matches: boolean; matchedText?: string }) => void;
}

const RegexTester: React.FC<RegexTesterProps> = ({
  initialPattern = '',
  initialSampleText = '',
  onPatternChange,
  onTestResult
}) => {
  const [pattern, setPattern] = useState(initialPattern);
  const [sampleText, setSampleText] = useState(initialSampleText);
  const [result, setResult] = useState<{
    matches: boolean;
    matchedText?: string;
    error?: string;
  } | null>(null);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (onPatternChange) {
      onPatternChange(pattern);
    }
  }, [pattern, onPatternChange]);

  const handlePatternChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPattern(e.target.value);
    setResult(null);
  };

  const handleSampleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSampleText(e.target.value);
    setResult(null);
  };

  const handleTest = () => {
    if (!pattern || !sampleText) {
      setResult({
        matches: false,
        error: 'Por favor, preencha o padrão e o texto de exemplo.'
      });
      return;
    }

    try {
      const regex = new RegExp(pattern, 'g');
      const matches = sampleText.match(regex);
      
      const testResult = {
        matches: !!matches,
        matchedText: matches ? matches.join(', ') : undefined
      };
      
      setResult(testResult);
      
      if (onTestResult) {
        onTestResult(testResult);
      }
    } catch (error: any) {
      setResult({
        matches: false,
        error: `Erro na expressão regular: ${error.message}`
      });
    }
  };

  const handleCopyPattern = () => {
    navigator.clipboard.writeText(pattern);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const regexExamples = [
    { label: 'Qualquer número', pattern: '\\d+' },
    { label: 'Valor monetário', pattern: 'R\\$\\s*\\d+[,.]\\d{2}' },
    { label: 'Data (DD/MM/AAAA)', pattern: '\\d{2}/\\d{2}/\\d{4}' },
    { label: 'Transferência PIX', pattern: '(PIX|Pix|pix).*?(de|para|enviado|recebido)' },
    { label: 'Pagamento de boleto', pattern: '(Pagamento|PGTO).*?(boleto|fatura|conta)' },
    { label: 'Transferência bancária', pattern: '(TED|DOC|Transferência|Transferencia)' }
  ];

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Testador de Expressões Regulares
        <Tooltip title="As expressões regulares são padrões utilizados para encontrar combinações de caracteres em textos. Úteis para identificar transações específicas em extratos bancários.">
          <IconButton size="small" sx={{ ml: 1 }}>
            <HelpIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Padrão (Expressão Regular)"
            value={pattern}
            onChange={handlePatternChange}
            placeholder="Ex: \b(PIX|Pix|pix)\b"
            InputProps={{
              endAdornment: (
                <Tooltip title={copied ? "Copiado!" : "Copiar padrão"}>
                  <IconButton edge="end" onClick={handleCopyPattern}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              )
            }}
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Texto de exemplo"
            value={sampleText}
            onChange={handleSampleTextChange}
            placeholder="Ex: Transferência PIX recebida de João Silva"
            multiline
            rows={4}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleTest}
            startIcon={<TestIcon />}
          >
            Testar Expressão
          </Button>
        </Grid>
        
        {result && (
          <Grid item xs={12}>
            {result.error ? (
              <Alert severity="error">{result.error}</Alert>
            ) : result.matches ? (
              <Alert severity="success">
                <Typography variant="body1">
                  <strong>Padrão encontrado!</strong>
                </Typography>
                {result.matchedText && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Texto encontrado: <strong>{result.matchedText}</strong>
                  </Typography>
                )}
              </Alert>
            ) : (
              <Alert severity="warning">
                Nenhuma correspondência encontrada no texto de exemplo.
              </Alert>
            )}
          </Grid>
        )}
        
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle2" gutterBottom>
            Exemplos de padrões comuns:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            {regexExamples.map((example, index) => (
              <Chip
                key={index}
                label={example.label}
                onClick={() => {
                  setPattern(example.pattern);
                  setResult(null);
                }}
                clickable
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default RegexTester; 