const { Sequelize } = require('sequelize');
const config = require('../config/database');

const User = require('./User');
const Company = require('./Company');
const Plan = require('./Plan');
const Document = require('./Document');
const AccountingRule = require('./AccountingRule');
const Client = require('./Client');

const env = process.env.NODE_ENV || 'development';
const sequelize = new Sequelize(config[env]);

// Inicializar os modelos
User.init(sequelize);
Company.init(sequelize);
Plan.init(sequelize);
Document.init(sequelize);
AccountingRule.init(sequelize);
Client.init(sequelize);

// Configurar as associações
User.associate(sequelize.models);
Company.associate(sequelize.models);
Plan.associate(sequelize.models);
Document.associate(sequelize.models);
AccountingRule.associate(sequelize.models);

// Add Client association
Client.associate(sequelize.models);

module.exports = {
  sequelize,
  Sequelize,
  User,
  Company,
  Plan,
  Document,
  AccountingRule,
  Client
};