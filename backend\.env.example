# Configurações do Servidor
PORT=3000
NODE_ENV=development

# Configurações do Banco de Dados
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=mvp_contabil

# Configurações de Autenticação
JWT_SECRET=seu_jwt_secret_aqui
JWT_EXPIRATION=24h

# Configurações de Email
MAIL_HOST=smtp.exemplo.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=sua_senha
MAIL_FROM=<EMAIL>

# Configurações de Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=20971520 # 20MB em bytes
MAX_FILES=10

# URLs
FRONTEND_URL=http://localhost:3003
API_URL=http://localhost:3000/api

# Configurações de Armazenamento
STORAGE_TYPE=local # local ou s3
AWS_BUCKET_NAME=seu-bucket-s3
AWS_ACCESS_KEY_ID=sua_access_key
AWS_SECRET_ACCESS_KEY=sua_secret_key
AWS_REGION=us-east-1 