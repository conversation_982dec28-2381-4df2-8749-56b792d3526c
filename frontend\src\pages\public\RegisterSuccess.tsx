import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Divider,
  Grid,
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

const RegisterSuccess = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { email, planType } = location.state || { email: '', planType: '' };

  const getPlanName = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Plano Individual';
      case 'business_basic':
        return 'Plano Empresarial Básico';
      case 'business_plus':
        return 'Plano Empresarial Plus';
      case 'business_premium':
        return 'Plano Empresarial Premium';
      default:
        return 'Plano não identificado';
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <CheckCircleOutlineIcon color="success" sx={{ fontSize: 80, mb: 2 }} />
          <Typography variant="h4" component="h1" gutterBottom>
            Obrigado por se registrar no Nexlify Contábil
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Obrigado por se registrar no Nexlify Contábil
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Próximos Passos
            </Typography>
            <Box sx={{ pl: 2 }}>
              <Typography variant="body1" paragraph>
                1. Verifique seu e-mail <strong>{email}</strong> para confirmar sua conta.
              </Typography>
              <Typography variant="body1" paragraph>
                2. Após a confirmação, você poderá fazer login na plataforma.
              </Typography>
              <Typography variant="body1" paragraph>
                3. Complete seu perfil e comece a utilizar os recursos do seu plano.
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Detalhes do Plano
            </Typography>
            <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1 }}>
              <Typography variant="subtitle1" color="primary" gutterBottom>
                {getPlanName(planType)}
              </Typography>
              <Typography variant="body2" paragraph>
                Seu plano será ativado assim que sua conta for confirmada.
              </Typography>
              <Typography variant="body2">
                Nossa equipe entrará em contato em breve para auxiliar nos primeiros passos.
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => navigate('/login')}
            sx={{ mr: 2 }}
          >
            Ir para Login
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate('/')}
          >
            Voltar para Home
          </Button>
        </Box>
      </Paper>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Precisa de ajuda? Entre em contato com nosso suporte em{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Typography>
      </Box>
    </Container>
  );
};

export default RegisterSuccess; 