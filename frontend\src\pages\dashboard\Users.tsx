import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar,
  InputAdornment,
  Avatar,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  FilterList as FilterIcon,
  PersonAdd as PersonAddIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

import userService, { User, UserFilter, CreateUserData, UpdateUserData } from '../../services/userService';
import { useAuth } from '../../contexts/AuthContext';

const Users: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filters, setFilters] = useState<UserFilter>({
    role: '',
    isActive: undefined,
    search: '',
    page: 1,
    limit: 10
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [totalUsers, setTotalUsers] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<CreateUserData | UpdateUserData>({
    name: '',
    email: '',
    password: '',
    role: 'accountant',
    phone: ''
  });

  // Carregar usuários ao montar o componente
  useEffect(() => {
    fetchUsers();
  }, [filters.page, filters.limit]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.getUsers(filters);
      setUsers(response.users);
      setTotalUsers(response.total);
    } catch (err) {
      console.error('Erro ao carregar usuários:', err);
      setError('Não foi possível carregar os usuários. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleFilterChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    setFilters(prev => ({ ...prev, role: e.target.value as string }));
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    const value = e.target.value as string;
    setFilters(prev => ({ 
      ...prev, 
      isActive: value === '' ? undefined : value === 'active' 
    }));
  };

  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 })); // Resetar para a primeira página
    fetchUsers();
  };

  const resetFilters = () => {
    setFilters({
      role: '',
      isActive: undefined,
      search: '',
      page: 1,
      limit: 10
    });
    fetchUsers();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    setFormData(prev => ({ ...prev, role: e.target.value as 'admin' | 'manager' | 'accountant' | 'client' }));
  };

  const handleCreateUser = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Validar dados
      if (!formData.name || !formData.email || !formData.password || !formData.role) {
        setError('Preencha todos os campos obrigatórios.');
        setLoading(false);
        return;
      }
      
      await userService.createUser(formData as CreateUserData);
      setSuccess('Usuário criado com sucesso!');
      setOpenCreateDialog(false);
      resetForm();
      fetchUsers();
    } catch (err: any) {
      console.error('Erro ao criar usuário:', err);
      setError(err.response?.data?.message || 'Não foi possível criar o usuário. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Validar dados
      if (!formData.name || !formData.email || !formData.role) {
        setError('Preencha todos os campos obrigatórios.');
        setLoading(false);
        return;
      }
      
      // Remover senha se estiver vazia
      const userData = { ...formData };
      if (!userData.password) {
        delete userData.password;
      }
      
      await userService.updateUser(selectedUser.id, userData);
      setSuccess('Usuário atualizado com sucesso!');
      setOpenEditDialog(false);
      resetForm();
      fetchUsers();
    } catch (err: any) {
      console.error('Erro ao atualizar usuário:', err);
      setError(err.response?.data?.message || 'Não foi possível atualizar o usuário. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    try {
      setLoading(true);
      setError(null);
      await userService.deleteUser(selectedUser.id);
      setSuccess('Usuário excluído com sucesso!');
      setOpenDeleteDialog(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (err: any) {
      console.error('Erro ao excluir usuário:', err);
      setError(err.response?.data?.message || 'Não foi possível excluir o usuário. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (user: User) => {
    try {
      setLoading(true);
      setError(null);
      await userService.toggleUserStatus(user.id, !user.isActive);
      setSuccess(`Usuário ${user.isActive ? 'desativado' : 'ativado'} com sucesso!`);
      fetchUsers();
    } catch (err: any) {
      console.error('Erro ao alterar status do usuário:', err);
      setError(err.response?.data?.message || 'Não foi possível alterar o status do usuário. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      password: '',
      role: 'accountant',
      phone: ''
    });
    setShowPassword(false);
  };

  const handleOpenEditDialog = (user: User) => {
    setSelectedUser(user);
    setFormData({
      name: user.name,
      email: user.email,
      role: user.role,
      phone: user.phone || ''
    });
    setOpenEditDialog(true);
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrador';
      case 'manager':
        return 'Gestor';
      case 'accountant':
        return 'Contador';
      case 'client':
        return 'Cliente';
      default:
        return role;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'manager':
        return 'warning';
      case 'accountant':
        return 'info';
      case 'client':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  // Verificar se o usuário atual tem permissão para gerenciar usuários
  const canManageUsers = currentUser && ['admin', 'manager'].includes(currentUser.role);

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Gerenciamento de Usuários
      </Typography>
      
      <Snackbar 
        open={!!error} 
        autoHideDuration={6000} 
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
      
      <Snackbar 
        open={!!success} 
        autoHideDuration={6000} 
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
      
      <Paper sx={{ width: '100%', mb: 3, p: 2 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Button 
              variant="outlined" 
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{ mr: 1 }}
            >
              {showFilters ? 'Ocultar Filtros' : 'Mostrar Filtros'}
            </Button>
            
            <Button 
              variant="outlined" 
              startIcon={<RefreshIcon />}
              onClick={fetchUsers}
              disabled={loading}
            >
              Atualizar
            </Button>
          </Box>
          
          {canManageUsers && (
            <Button 
              variant="contained" 
              color="primary" 
              startIcon={<PersonAddIcon />}
              onClick={() => {
                resetForm();
                setOpenCreateDialog(true);
              }}
            >
              Novo Usuário
            </Button>
          )}
        </Box>
        
        {showFilters && (
          <Box sx={{ mb: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Buscar"
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                  placeholder="Nome, email ou telefone"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Função</InputLabel>
                  <Select
                    value={filters.role}
                    onChange={handleRoleFilterChange}
                    label="Função"
                    name="role"
                  >
                    <MenuItem value="">Todas</MenuItem>
                    <MenuItem value="admin">Administrador</MenuItem>
                    <MenuItem value="manager">Gestor</MenuItem>
                    <MenuItem value="accountant">Contador</MenuItem>
                    <MenuItem value="client">Cliente</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.isActive === undefined ? '' : filters.isActive ? 'active' : 'inactive'}
                    onChange={handleStatusFilterChange}
                    label="Status"
                    name="isActive"
                  >
                    <MenuItem value="">Todos</MenuItem>
                    <MenuItem value="active">Ativo</MenuItem>
                    <MenuItem value="inactive">Inativo</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3} sx={{ display: 'flex', alignItems: 'center' }}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={applyFilters}
                  startIcon={<SearchIcon />}
                  sx={{ mr: 1 }}
                >
                  Filtrar
                </Button>
                <Button 
                  variant="outlined" 
                  onClick={resetFilters}
                >
                  Limpar
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : users.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            Nenhum usuário encontrado. {canManageUsers && 'Clique em "Novo Usuário" para adicionar.'}
          </Alert>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Nome</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Email</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Função</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Status</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>Criado em</th>
                  <th style={{ padding: '12px', textAlign: 'center' }}>Ações</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id} style={{ borderBottom: '1px solid #eee' }}>
                    <td style={{ padding: '12px' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar 
                          src={user.avatar} 
                          alt={user.name} 
                          sx={{ width: 32, height: 32, mr: 1 }}
                        >
                          {user.name.charAt(0)}
                        </Avatar>
                        {user.name}
                      </Box>
                    </td>
                    <td style={{ padding: '12px' }}>{user.email}</td>
                    <td style={{ padding: '12px' }}>
                      <Chip 
                        label={getRoleName(user.role)} 
                        color={getRoleColor(user.role) as any}
                        size="small"
                      />
                    </td>
                    <td style={{ padding: '12px' }}>
                      <Chip 
                        label={user.isActive ? 'Ativo' : 'Inativo'} 
                        color={user.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </td>
                    <td style={{ padding: '12px' }}>{formatDate(user.createdAt)}</td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      {canManageUsers && (
                        <>
                          <Tooltip title="Editar">
                            <IconButton 
                              size="small" 
                              onClick={() => handleOpenEditDialog(user)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={user.isActive ? 'Desativar' : 'Ativar'}>
                            <IconButton 
                              size="small" 
                              color={user.isActive ? 'default' : 'success'}
                              onClick={() => handleToggleUserStatus(user)}
                            >
                              {user.isActive ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Excluir">
                            <IconButton 
                              size="small" 
                              color="error"
                              onClick={() => {
                                setSelectedUser(user);
                                setOpenDeleteDialog(true);
                              }}
                              disabled={user.id === currentUser?.id} // Não permitir excluir o próprio usuário
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        )}
      </Paper>
      
      {/* Dialog de criação de usuário */}
      <Dialog
        open={openCreateDialog}
        onClose={() => setOpenCreateDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Novo Usuário</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Senha"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange}
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Função</InputLabel>
                <Select
                  value={formData.role}
                  onChange={handleRoleChange}
                  label="Função"
                  name="role"
                  required
                >
                  {currentUser?.role === 'admin' && (
                    <MenuItem value="admin">Administrador</MenuItem>
                  )}
                  {['admin', 'manager'].includes(currentUser?.role || '') && (
                    <MenuItem value="manager">Gestor</MenuItem>
                  )}
                  <MenuItem value="accountant">Contador</MenuItem>
                  <MenuItem value="client">Cliente</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Telefone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCreateDialog(false)}>Cancelar</Button>
          <Button 
            onClick={handleCreateUser} 
            variant="contained" 
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Criar'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Dialog de edição de usuário */}
      <Dialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Editar Usuário</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nova Senha (deixe em branco para manter a atual)"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Função</InputLabel>
                <Select
                  value={formData.role}
                  onChange={handleRoleChange}
                  label="Função"
                  name="role"
                  required
                  disabled={selectedUser?.id === currentUser?.id} // Não permitir alterar a própria função
                >
                  {currentUser?.role === 'admin' && (
                    <MenuItem value="admin">Administrador</MenuItem>
                  )}
                  {['admin', 'manager'].includes(currentUser?.role || '') && (
                    <MenuItem value="manager">Gestor</MenuItem>
                  )}
                  <MenuItem value="accountant">Contador</MenuItem>
                  <MenuItem value="client">Cliente</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Telefone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </Grid>
            {selectedUser?.id !== currentUser?.id && (
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive === undefined ? selectedUser?.isActive : formData.isActive}
                      onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                      name="isActive"
                    />
                  }
                  label="Usuário Ativo"
                />
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>Cancelar</Button>
          <Button 
            onClick={handleUpdateUser} 
            variant="contained" 
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Dialog de confirmação de exclusão */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o usuário "{selectedUser?.name}"?
            Esta ação não pode ser desfeita.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancelar</Button>
          <Button 
            onClick={handleDeleteUser} 
            color="error" 
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Excluir'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Users; 