const { Model, DataTypes } = require('sequelize');

class AccountingRule extends Model {
  static init(sequelize) {
    super.init({
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      rule_type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      conditions: {
        type: DataTypes.JSON,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      actions: {
        type: DataTypes.JSON,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      company_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'companies',
          key: 'id'
        }
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      }
    }, {
      sequelize,
      tableName: 'accounting_rules',
      hooks: {
        beforeCreate: async (rule) => {
          // Validar a estrutura das condições e ações
          if (!Array.isArray(rule.conditions)) {
            throw new Error('Conditions must be an array');
          }
          if (!Array.isArray(rule.actions)) {
            throw new Error('Actions must be an array');
          }
        }
      }
    });
  }

  static associate(models) {
    this.belongsTo(models.Company, { foreignKey: 'company_id', as: 'company' });
  }
}

module.exports = AccountingRule; 