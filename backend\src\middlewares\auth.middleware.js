const jwt = require('jsonwebtoken');
const { User, Company } = require('../models');

/**
 * Middleware para verificar se o usuário está autenticado
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Verificar se o token está presente no header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ 
        success: false,
        message: 'Token de autenticação não fornecido' 
      });
    }

    // Extrair o token do header (formato: "Bearer TOKEN")
    const token = authHeader.split(' ')[1];
    if (!token) {
      return res.status(401).json({ 
        success: false,
        message: 'Formato de token inválido' 
      });
    }

    // Verificar e decodificar o token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Buscar o usuário no banco de dados
    const user = await User.findByPk(decoded.id, {
      include: [
        { model: Company, as: 'company' }
      ]
    });

    if (!user) {
      return res.status(401).json({ 
        success: false,
        message: 'Usuário não encontrado' 
      });
    }

    // Verificar se o usuário está ativo
    if (!user.is_active) {
      return res.status(403).json({ 
        success: false,
        message: 'Usuário desativado' 
      });
    }

    // Adicionar o usuário ao objeto de requisição
    req.user = user;
    
    // Atualizar o último login do usuário
    await user.update({ last_login: new Date() });
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false,
        message: 'Token inválido' 
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false,
        message: 'Token expirado' 
      });
    }
    console.error('Erro de autenticação:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Erro interno do servidor' 
    });
  }
};

/**
 * Middleware para verificar se o usuário tem a role necessária
 * @param {Array} roles - Array de roles permitidas
 */
exports.authorize = (roles = []) => {
  if (typeof roles === 'string') {
    roles = [roles];
  }

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false,
        message: 'Usuário não autenticado' 
      });
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({ 
        success: false,
        message: 'Acesso não autorizado' 
      });
    }

    next();
  };
};

/**
 * Middleware para verificar se o usuário pertence à mesma empresa
 */
exports.sameCompany = async (req, res, next) => {
  try {
    // Verificar se o usuário está autenticado
    if (!req.user) {
      return res.status(401).json({ 
        success: false,
        message: 'Usuário não autenticado' 
      });
    }

    // Administradores têm acesso a todas as empresas
    if (req.user.role === 'admin') {
      return next();
    }

    // Verificar se o ID da empresa está presente na requisição
    const companyId = req.params.company_id || req.body.company_id;
    if (!companyId) {
      return res.status(400).json({ 
        success: false,
        message: 'ID da empresa não fornecido' 
      });
    }

    // Verificar se o usuário pertence à empresa
    if (req.user.company_id !== companyId) {
      return res.status(403).json({ 
        success: false,
        message: 'Acesso não autorizado a esta empresa' 
      });
    }

    next();
  } catch (error) {
    console.error('Erro ao verificar empresa:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Erro interno do servidor' 
    });
  }
}; 