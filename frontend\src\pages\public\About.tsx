import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Avatar,
  Card,
  CardContent,
  Divider,
} from '@mui/material';

const About: React.FC = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'CEO & Fundador',
      bio: 'Contador com mais de 15 anos de experiência, especialista em tecnologia para contabilidade.',
      avatar: '/static/images/team/joao.jpg',
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      bio: 'Desenvolvedora de software com foco em soluções para o setor contábil e financeiro.',
      avatar: '/static/images/team/maria.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Diretor de Operações',
      bio: 'Especialista em processos contábeis e implementação de soluções tecnológicas.',
      avatar: '/static/images/team/pedro.jpg',
    },
  ];

  return (
    <Box>
      {/* Seção de Cabeçalho */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h1" gutterBottom>
            Sobre o Nexlify Contábil
          </Typography>
          <Typography variant="h6" sx={{ opacity: 0.8 }}>
            Transformando a gestão contábil com tecnologia e inovação
          </Typography>
        </Container>
      </Box>

      {/* Seção Nossa História */}
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Typography variant="h4" component="h2" gutterBottom align="center">
          Nossa História
        </Typography>
        <Divider sx={{ mb: 4 }} />
        <Typography variant="body1" paragraph>
          A Nexlify Contábil nasceu da necessidade de simplificar e automatizar processos contábeis que tradicionalmente
          consomem muito tempo e recursos das empresas. Fundada em 2022 por um grupo de profissionais com experiência
          em contabilidade e tecnologia, nossa missão é transformar a maneira como as empresas gerenciam seus documentos
          e processos contábeis.
        </Typography>
        <Typography variant="body1" paragraph>
          Começamos como uma pequena startup com o objetivo de resolver um problema específico: a gestão e processamento
          de extratos bancários. Com o tempo, expandimos nossa solução para abranger uma gama mais ampla de documentos
          contábeis e fiscais, sempre com foco na automação e eficiência.
        </Typography>
        <Typography variant="body1">
          Hoje, atendemos centenas de escritórios contábeis e empresas de diversos portes, ajudando-os a economizar
          tempo, reduzir erros e focar no que realmente importa: o crescimento de seus negócios.
        </Typography>
      </Container>

      {/* Seção Nossa Missão e Valores */}
      <Box sx={{ bgcolor: 'grey.100', py: 8 }}>
        <Container maxWidth="md">
          <Typography variant="h4" component="h2" gutterBottom align="center">
            Missão e Valores
          </Typography>
          <Divider sx={{ mb: 4 }} />
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h5" component="h3" gutterBottom color="primary">
                    Nossa Missão
                  </Typography>
                  <Typography variant="body1">
                    Simplificar a gestão contábil através da tecnologia, permitindo que empresas e profissionais
                    contábeis foquem em análises estratégicas e no crescimento de seus negócios, em vez de tarefas
                    operacionais repetitivas.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h5" component="h3" gutterBottom color="primary">
                    Nossos Valores
                  </Typography>
                  <Typography variant="body1" component="ul" sx={{ pl: 2 }}>
                    <li>Inovação constante em nossas soluções</li>
                    <li>Compromisso com a qualidade e precisão</li>
                    <li>Foco na experiência e satisfação do cliente</li>
                    <li>Transparência em todas as nossas operações</li>
                    <li>Responsabilidade com dados e informações sensíveis</li>
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Seção Nossa Equipe */}
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Typography variant="h4" component="h2" gutterBottom align="center">
          Nossa Equipe
        </Typography>
        <Divider sx={{ mb: 4 }} />
        <Grid container spacing={4}>
          {teamMembers.map((member, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent>
                  <Avatar
                    src={member.avatar}
                    alt={member.name}
                    sx={{
                      width: 120,
                      height: 120,
                      mx: 'auto',
                      mb: 2,
                      bgcolor: 'primary.main',
                    }}
                  >
                    {member.name.charAt(0)}
                  </Avatar>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {member.name}
                  </Typography>
                  <Typography variant="subtitle1" color="primary" gutterBottom>
                    {member.role}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {member.bio}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Seção Contato */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 6, textAlign: 'center' }}>
        <Container maxWidth="md">
          <Typography variant="h4" component="h2" gutterBottom>
            Entre em Contato
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Estamos sempre disponíveis para ajudar e responder a quaisquer perguntas
          </Typography>
          <Typography variant="h6">
            <EMAIL> | (11) 9999-9999
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default About; 