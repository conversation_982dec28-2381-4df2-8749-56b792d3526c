const { sequelize } = require('../models');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  try {
    // Criar o banco de dados
    await sequelize.query(`CREATE DATABASE IF NOT EXISTS ${sequelize.config.database};`);
    
    // Usar o banco de dados
    await sequelize.query(`USE ${sequelize.config.database};`);

    // Sincronizar os modelos com o banco
    await sequelize.sync({ force: true });

    console.log('Banco de dados inicializado com sucesso!');

    // Executar seeds se necessário
    const seedPath = path.join(__dirname, 'seeds', 'initial-data.js');
    if (fs.existsSync(seedPath)) {
      const seeds = require(seedPath);
      await seeds.up(sequelize.queryInterface, sequelize);
      console.log('Dados iniciais inseridos com sucesso!');
    }

  } catch (error) {
    console.error('Erro ao inicializar banco de dados:', error);
  } finally {
    await sequelize.close();
  }
}

initDatabase(); 