const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const { User, Company, Plan } = require('../models');
const { Op } = require('sequelize');

/**
 * Gerar tokens de acesso e refresh
 * @param {Object} user - Objeto do usuário
 * @returns {Object} - Tokens gerados
 */
const generateTokens = async (user) => {
  // Gerar token JWT de acesso
  const accessToken = jwt.sign(
    {
      id: user.id,
      role: user.role,
      company_id: user.company?.id,
      plan_id: user.plan?.id
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );

  // Gerar refresh token
  const refreshToken = crypto.randomBytes(40).toString('hex');
  const refreshTokenExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 dias

  // <PERSON>var refresh token no banco de dados
  await user.update({
    refresh_token: refreshToken,
    refresh_token_expires: refreshTokenExpires
  });

  return {
    accessToken,
    refreshToken,
    refreshTokenExpires
  };
};

/**
 * Registrar um novo usuário
 * @route POST /api/auth/register
 * @access Public
 */
exports.register = async (req, res) => {
  try {
    const { name, email, password, role } = req.body;

    // Verificar se o email já está em uso
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'Email já está em uso' });
    }

    // Hash da senha
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Criar um novo usuário
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      role: role || 'client'
    });

    // Gerar tokens de acesso e refresh
    const { accessToken, refreshToken } = await generateTokens(user);

    // Retornar os dados do usuário e os tokens
    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role
        },
        token: accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Erro ao registrar usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao registrar usuário',
      error: error.message
    });
  }
};

/**
 * Autenticar usuário e gerar token
 * @route POST /api/auth/login
 * @access Public
 */
exports.login = async (req, res) => {
  try {
    console.log('Tentativa de login:', req.body);
    const { email, password } = req.body;

    // Verificar se o email e senha foram fornecidos
    if (!email || !password) {
      console.log('Email ou senha não fornecidos');
      return res.status(400).json({
        success: false,
        message: 'Email e senha são obrigatórios'
      });
    }

    // Buscar o usuário pelo email
    const user = await User.findOne({
      where: { email },
      include: [
        { model: Company, as: 'company' },
        { model: Plan, as: 'plan' }
      ]
    });

    console.log('Usuário encontrado:', user ? user.toJSON() : null);

    if (!user) {
      console.log('Usuário não encontrado');
      return res.status(401).json({
        success: false,
        message: 'Credenciais inválidas'
      });
    }

    // Verificar se a senha está correta
    const isMatch = await bcrypt.compare(password, user.password);
    console.log('Senha correta:', isMatch);

    if (!isMatch) {
      console.log('Senha incorreta');
      return res.status(401).json({
        success: false,
        message: 'Credenciais inválidas'
      });
    }

    // Atualizar último login
    await user.update({ last_login: new Date() });

    // Gerar tokens de acesso e refresh
    const { accessToken, refreshToken } = await generateTokens(user);

    console.log('Tokens gerados:', { accessToken, refreshToken });

    // Retornar os dados do usuário e os tokens
    const response = {
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          company: user.company ? {
            id: user.company.id,
            name: user.company.name
          } : null,
          plan: user.plan ? {
            id: user.plan.id,
            name: user.plan.name
          } : null
        },
        token: accessToken,
        refreshToken
      }
    };

    console.log('Resposta do login:', response);
    res.json(response);
  } catch (error) {
    console.error('Erro ao autenticar usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao autenticar usuário',
      error: error.message
    });
  }
};

/**
 * Obter dados do usuário atual
 * @route GET /api/auth/me
 * @access Private
 */
exports.getMe = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: [
        { model: Company, as: 'company' },
        { model: Plan, as: 'plan' }
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        company: user.company ? {
          id: user.company.id,
          name: user.company.name
        } : null,
        plan: user.plan ? {
          id: user.plan.id,
          name: user.plan.name
        } : null
      }
    });
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter dados do usuário',
      error: error.message
    });
  }
};

/**
 * Solicitar redefinição de senha
 * @route POST /api/auth/forgot-password
 * @access Public
 */
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Verificar se o email foi fornecido
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email é obrigatório'
      });
    }

    // Buscar o usuário pelo email
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Gerar token de redefinição de senha
    const resetToken = crypto.randomBytes(20).toString('hex');
    const resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
    const resetPasswordExpires = new Date(Date.now() + 3600000); // 1 hora

    // Atualizar o usuário com o token
    await user.update({
      reset_password_token: resetPasswordToken,
      reset_password_expires: resetPasswordExpires
    });

    // Aqui você enviaria um email com o link para redefinição de senha
    // Exemplo: `${req.protocol}://${req.get('host')}/reset-password/${resetToken}`

    res.json({
      success: true,
      message: 'Email de redefinição de senha enviado'
    });
  } catch (error) {
    console.error('Erro ao solicitar redefinição de senha:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao solicitar redefinição de senha',
      error: error.message
    });
  }
};

/**
 * Atualizar token usando refresh token
 * @route POST /api/auth/refresh-token
 * @access Public
 */
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    // Verificar se o refresh token foi fornecido
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token é obrigatório'
      });
    }

    // Buscar o usuário pelo refresh token
    const user = await User.findOne({
      where: {
        refresh_token: refreshToken,
        refresh_token_expires: { [Op.gt]: new Date() }
      },
      include: [
        { model: Company, as: 'company' },
        { model: Plan, as: 'plan' }
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token inválido ou expirado'
      });
    }

    // Gerar novos tokens
    const tokens = await generateTokens(user);

    // Retornar os novos tokens
    res.json({
      success: true,
      data: {
        token: tokens.accessToken,
        refreshToken: tokens.refreshToken
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar token:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao atualizar token',
      error: error.message
    });
  }
};

/**
 * Redefinir senha
 * @route POST /api/auth/reset-password/:token
 * @access Public
 */
exports.resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    // Verificar se a senha foi fornecida
    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Nova senha é obrigatória'
      });
    }

    // Hash do token
    const resetPasswordToken = crypto.createHash('sha256').update(token).digest('hex');

    // Buscar o usuário pelo token
    const user = await User.findOne({
      where: {
        reset_password_token: resetPasswordToken,
        reset_password_expires: { [Op.gt]: new Date() }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Token inválido ou expirado'
      });
    }

    // Hash da nova senha
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Atualizar a senha e remover o token
    await user.update({
      password: hashedPassword,
      reset_password_token: null,
      reset_password_expires: null
    });

    res.json({
      success: true,
      message: 'Senha redefinida com sucesso'
    });
  } catch (error) {
    console.error('Erro ao redefinir senha:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao redefinir senha',
      error: error.message
    });
  }
};