import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { planService } from '../../services/api';

// Interface para os planos
interface Plan {
  name: string;
  description: string;
  price: number;
  additionalClientPrice: number;
  maxUsers: number;
  maxClients: number;
  maxDocumentsPerMonth: number;
  maxTransactionsPerMonth: number;
  storageLimit: number;
  features: string[];
  planType: 'individual' | 'business_basic' | 'business_plus' | 'business_premium';
  isActive: boolean;
}

const PlanSelection: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await planService.getPublicPlans();
        
        if (response.success) {
          setPlans(response.data);
        } else {
          setError('Erro ao carregar planos. Por favor, tente novamente.');
        }
      } catch (error) {
        console.error('Erro ao buscar planos:', error);
        setError('Erro ao carregar planos. Por favor, tente novamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSelectPlan = (planType: string) => {
    navigate(`/registro?plano=${planType}`);
  };

  // Filtrar planos com base na aba selecionada
  const filteredPlans = plans.filter(plan => {
    if (tabValue === 0) {
      return plan.planType === 'individual';
    } else {
      return plan.planType.startsWith('business_');
    }
  });

  // Formatar preço
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  return (
    <Box sx={{ py: 8, bgcolor: 'background.default' }}>
      <Container maxWidth="lg">
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Escolha o Plano Ideal
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto' }}>
            Selecione o plano que melhor atende às necessidades do seu negócio
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            centered
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: 'primary.main',
              },
            }}
          >
            <Tab label="Plano Individual" />
            <Tab label="Planos Empresariais" />
          </Tabs>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
            <Button
              color="inherit"
              size="small"
              onClick={() => window.location.reload()}
              sx={{ ml: 2 }}
            >
              Tentar novamente
            </Button>
          </Alert>
        ) : (
          <Grid container spacing={4} justifyContent={filteredPlans.length < 3 ? 'center' : 'flex-start'}>
            {filteredPlans.map((plan) => (
              <Grid item xs={12} sm={6} md={4} key={plan.planType}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 3,
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: 6,
                    },
                    ...(plan.planType === 'business_premium' && {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                      borderStyle: 'solid',
                    }),
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    {plan.planType === 'business_premium' && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          bgcolor: 'primary.main',
                          color: 'white',
                          px: 2,
                          py: 0.5,
                          borderBottomLeftRadius: 8,
                        }}
                      >
                        <Typography variant="subtitle2">Mais Popular</Typography>
                      </Box>
                    )}

                    <Typography
                      variant="h5"
                      component="h2"
                      gutterBottom
                      sx={{ fontWeight: 600, textAlign: 'center' }}
                    >
                      {plan.name}
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 3, textAlign: 'center', height: '40px' }}
                    >
                      {plan.description}
                    </Typography>

                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                      <Typography variant="h3" component="div" sx={{ fontWeight: 700 }}>
                        {formatPrice(plan.price)}
                        <Typography variant="subtitle1" component="span" sx={{ fontWeight: 400 }}>
                          /mês
                        </Typography>
                      </Typography>
                      {plan.additionalClientPrice > 0 && (
                        <Typography variant="body2" color="text.secondary">
                          + {formatPrice(plan.additionalClientPrice)} por cliente adicional acima de{' '}
                          {plan.maxClients}
                        </Typography>
                      )}
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <List dense>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <CheckCircleOutlineIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${plan.maxUsers === 9999 ? 'Usuários ilimitados' : `Até ${plan.maxUsers} usuários`}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <CheckCircleOutlineIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${plan.maxClients === 9999 ? 'Clientes ilimitados' : `Até ${plan.maxClients} clientes`}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <CheckCircleOutlineIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${plan.maxDocumentsPerMonth >= 9999999 ? 'Documentos ilimitados' : `Até ${plan.maxDocumentsPerMonth} documentos/mês`}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <CheckCircleOutlineIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${plan.maxTransactionsPerMonth >= 9999999 ? 'Transações ilimitadas' : `Até ${plan.maxTransactionsPerMonth} transações/mês`}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <CheckCircleOutlineIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={`${plan.storageLimit}GB de armazenamento`} />
                      </ListItem>
                    </List>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                      Recursos inclusos:
                    </Typography>
                    <List dense>
                      {plan.features.map((feature, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <CheckCircleOutlineIcon color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={feature} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>

                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      fullWidth
                      size="large"
                      onClick={() => handleSelectPlan(plan.planType)}
                      sx={{ py: 1.5 }}
                    >
                      Selecionar Plano
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        <Box sx={{ mt: 8, textAlign: 'center' }}>
          <Typography variant="body1" paragraph>
            Todos os planos incluem suporte técnico e atualizações gratuitas.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Precisa de um plano personalizado? <Button color="primary">Entre em contato</Button>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default PlanSelection; 