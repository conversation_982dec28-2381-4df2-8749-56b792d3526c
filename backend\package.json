{"name": "nexlify-contabil-backend", "version": "1.0.0", "description": "Backend para o sistema Nexlify Contábil", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "db:init": "node src/database/init.js", "db:seed": "sequelize-cli db:seed:all", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop"}, "keywords": ["contabilidade", "documentos", "processamento", "saas"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lodash.isplainobject": "^4.0.6", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.1", "sequelize": "^6.35.2", "tesseract.js": "^4.0.3", "winston": "^3.11.0", "yup": "^1.2.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}}