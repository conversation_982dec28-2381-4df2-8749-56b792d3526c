import React, { useState, useRef } from 'react';
import { 
  Box, 
  Button, 
  Typography, 
  Paper, 
  LinearProgress, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { 
  CloudUpload as CloudUploadIcon, 
  InsertDriveFile as FileIcon, 
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

interface FileUploadProps {
  onUpload: (files: File[]) => Promise<void>;
  acceptedFileTypes?: string;
  maxFiles?: number;
  maxFileSize?: number; // em bytes
  title?: string;
  description?: string;
}

interface FileWithStatus {
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  acceptedFileTypes = '*',
  maxFiles = 10,
  maxFileSize = 10 * 1024 * 1024, // 10MB por padrão
  title = 'Upload de Arquivos',
  description = 'Arraste e solte arquivos aqui ou clique para selecionar'
}) => {
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Verificar tamanho do arquivo
    if (file.size > maxFileSize) {
      return { 
        valid: false, 
        error: `O arquivo excede o tamanho máximo de ${(maxFileSize / (1024 * 1024)).toFixed(2)}MB` 
      };
    }

    // Verificar tipo de arquivo se especificado
    if (acceptedFileTypes !== '*') {
      const fileType = file.type;
      const acceptedTypes = acceptedFileTypes.split(',').map(type => type.trim());
      
      if (!acceptedTypes.some(type => {
        // Verificar por extensão ou MIME type
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return fileType === type || fileType.startsWith(`${type.split('/')[0]}/`);
      })) {
        return { 
          valid: false, 
          error: `Tipo de arquivo não permitido. Tipos aceitos: ${acceptedFileTypes}` 
        };
      }
    }

    return { valid: true };
  };

  const processFiles = (fileList: FileList) => {
    const newFiles: FileWithStatus[] = [];
    
    // Verificar se não excede o número máximo de arquivos
    if (files.length + fileList.length > maxFiles) {
      alert(`Você pode enviar no máximo ${maxFiles} arquivos.`);
      return;
    }

    Array.from(fileList).forEach(file => {
      const validation = validateFile(file);
      
      if (validation.valid) {
        newFiles.push({
          file,
          status: 'pending',
          progress: 0
        });
      } else {
        alert(validation.error);
      }
    });

    setFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
      // Limpar o input para permitir selecionar o mesmo arquivo novamente
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (files.length === 0 || isUploading) return;
    
    setIsUploading(true);
    
    // Atualizar status de todos os arquivos para 'uploading'
    setFiles(prev => prev.map(file => ({
      ...file,
      status: 'uploading',
      progress: 0
    })));

    try {
      // Simular progresso de upload
      const uploadInterval = setInterval(() => {
        setFiles(prev => prev.map(file => {
          if (file.status === 'uploading' && file.progress < 90) {
            return {
              ...file,
              progress: file.progress + 10
            };
          }
          return file;
        }));
      }, 300);

      // Chamar a função de upload fornecida pelo componente pai
      await onUpload(files.map(f => f.file));
      
      clearInterval(uploadInterval);
      
      // Marcar todos os arquivos como enviados com sucesso
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'success',
        progress: 100
      })));
      
      // Limpar a lista após alguns segundos
      setTimeout(() => {
        setFiles([]);
      }, 3000);
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      
      // Marcar arquivos como erro
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'error',
        progress: 0,
        error: 'Falha no upload'
      })));
    } finally {
      setIsUploading(false);
    }
  };

  const getFileIcon = (file: FileWithStatus) => {
    switch (file.status) {
      case 'success':
        return <CheckCircleIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'uploading':
        return <CircularProgress size={24} />;
      default:
        return <FileIcon />;
    }
  };

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 3, 
        mb: 3,
        border: isDragging ? '2px dashed #1976d2' : '2px dashed #ccc',
        backgroundColor: isDragging ? 'rgba(25, 118, 210, 0.04)' : 'transparent',
        transition: 'all 0.3s ease'
      }}
    >
      <Typography variant="h6" gutterBottom align="center">
        {title}
      </Typography>
      
      <Box
        sx={{ 
          p: 3, 
          textAlign: 'center',
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)'
          }
        }}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="body1" gutterBottom>
          {description}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Tamanho máximo: {(maxFileSize / (1024 * 1024)).toFixed(2)}MB | Máximo de arquivos: {maxFiles}
        </Typography>
        
        <VisuallyHiddenInput
          type="file"
          multiple
          ref={fileInputRef}
          accept={acceptedFileTypes}
          onChange={handleFileInputChange}
        />
      </Box>

      {files.length > 0 && (
        <>
          <List sx={{ mt: 2 }}>
            {files.map((file, index) => (
              <ListItem
                key={`${file.file.name}-${index}`}
                secondaryAction={
                  file.status !== 'uploading' && (
                    <IconButton edge="end" onClick={() => handleRemoveFile(index)}>
                      <DeleteIcon />
                    </IconButton>
                  )
                }
              >
                <ListItemIcon>
                  {getFileIcon(file)}
                </ListItemIcon>
                <ListItemText 
                  primary={file.file.name} 
                  secondary={
                    file.status === 'error' 
                      ? file.error 
                      : `${(file.file.size / 1024).toFixed(2)} KB`
                  } 
                />
                {file.status === 'uploading' && (
                  <Box sx={{ width: '100%', ml: 2, mr: 2 }}>
                    <LinearProgress variant="determinate" value={file.progress} />
                  </Box>
                )}
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleUpload}
              disabled={isUploading || files.every(f => f.status === 'success')}
              startIcon={isUploading ? <CircularProgress size={20} color="inherit" /> : undefined}
            >
              {isUploading ? 'Enviando...' : 'Enviar Arquivos'}
            </Button>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default FileUpload; 