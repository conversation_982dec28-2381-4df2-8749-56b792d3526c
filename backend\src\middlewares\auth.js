const jwt = require('jsonwebtoken');
const { createError } = require('../utils/error');
const User = require('../models/User');

/**
 * Middleware para proteger rotas (requer autenticação)
 * @param {Request} req - Objeto de requisição
 * @param {Response} res - Objeto de resposta
 * @param {Function} next - Função next
 */
exports.protect = async (req, res, next) => {
  try {
    let token;

    // Verificar se o token está presente no header Authorization
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Verificar se o token existe
    if (!token) {
      return next(createError(401, 'Não autorizado, token não fornecido'));
    }

    try {
      // Verificar o token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

      // Verificar se o usuário existe
      const user = await User.findById(decoded.id);

      if (!user) {
        return next(createError(401, 'Usuário não encontrado'));
      }

      // Verificar se o usuário está ativo
      if (!user.isActive) {
        return next(createError(401, 'Conta desativada. Entre em contato com o suporte.'));
      }

      // Adicionar o usuário à requisição
      req.user = user;
      next();
    } catch (error) {
      return next(createError(401, 'Token inválido ou expirado'));
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware para verificar permissões de administrador
 * @param {Request} req - Objeto de requisição
 * @param {Response} res - Objeto de resposta
 * @param {Function} next - Função next
 */
exports.admin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    return next(createError(403, 'Acesso negado. Permissão de administrador necessária.'));
  }
};

/**
 * Middleware para verificar permissões de gestor
 * @param {Request} req - Objeto de requisição
 * @param {Response} res - Objeto de resposta
 * @param {Function} next - Função next
 */
exports.manager = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'manager')) {
    next();
  } else {
    return next(createError(403, 'Acesso negado. Permissão de gestor necessária.'));
  }
};

/**
 * Middleware para verificar permissões de contador
 * @param {Request} req - Objeto de requisição
 * @param {Response} res - Objeto de resposta
 * @param {Function} next - Função next
 */
exports.accountant = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'accountant')) {
    next();
  } else {
    return next(createError(403, 'Acesso negado. Permissão de contador necessária.'));
  }
}; 