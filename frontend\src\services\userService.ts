import api from './api';

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'accountant' | 'client';
  isActive: boolean;
  company?: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
  phone?: string;
  avatar?: string;
}

export interface UserFilter {
  role?: string;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'manager' | 'accountant' | 'client';
  company?: string;
  phone?: string;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  password?: string;
  role?: 'admin' | 'manager' | 'accountant' | 'client';
  isActive?: boolean;
  company?: string;
  phone?: string;
}

const userService = {
  /**
   * Lista usuários com filtros opcionais
   * @param filters Filtros para a listagem
   * @returns Promise com a lista de usuários
   */
  async getUsers(filters?: UserFilter): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    const response = await api.get('/users', { params: filters });
    return response.data;
  },

  /**
   * Obtém um usuário pelo ID
   * @param id ID do usuário
   * @returns Promise com o usuário
   */
  async getUserById(id: string): Promise<User> {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  /**
   * Cria um novo usuário
   * @param userData Dados do usuário
   * @returns Promise com o usuário criado
   */
  async createUser(userData: CreateUserData): Promise<User> {
    const response = await api.post('/users', userData);
    return response.data;
  },

  /**
   * Atualiza um usuário existente
   * @param id ID do usuário
   * @param userData Dados do usuário
   * @returns Promise com o usuário atualizado
   */
  async updateUser(id: string, userData: UpdateUserData): Promise<User> {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  /**
   * Ativa ou desativa um usuário
   * @param id ID do usuário
   * @param isActive Status de ativação
   * @returns Promise com o usuário atualizado
   */
  async toggleUserStatus(id: string, isActive: boolean): Promise<User> {
    const response = await api.patch(`/users/${id}/status`, { isActive });
    return response.data;
  },

  /**
   * Exclui um usuário
   * @param id ID do usuário
   * @returns Promise vazia em caso de sucesso
   */
  async deleteUser(id: string): Promise<void> {
    await api.delete(`/users/${id}`);
  },

  /**
   * Altera a senha do usuário logado
   * @param currentPassword Senha atual
   * @param newPassword Nova senha
   * @returns Promise com mensagem de sucesso
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    const response = await api.post('/users/change-password', {
      currentPassword,
      newPassword
    });
    return response.data;
  },

  /**
   * Atualiza o perfil do usuário logado
   * @param userData Dados do usuário
   * @returns Promise com o usuário atualizado
   */
  async updateProfile(userData: {
    name?: string;
    email?: string;
    phone?: string;
    avatar?: File;
  }): Promise<User> {
    const formData = new FormData();
    
    if (userData.name) formData.append('name', userData.name);
    if (userData.email) formData.append('email', userData.email);
    if (userData.phone) formData.append('phone', userData.phone);
    if (userData.avatar) formData.append('avatar', userData.avatar);
    
    const response = await api.put('/users/profile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  }
};

export default userService; 