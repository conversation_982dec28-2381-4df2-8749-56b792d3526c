import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Upload as UploadIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';

interface DashboardStats {
  documentsCount: number;
  clientsCount: number;
  pendingDocuments: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        // Simular chamada à API
        // const response = await api.get('/dashboard/stats');
        // setStats(response.data);
        
        // Dados simulados para demonstração
        setTimeout(() => {
          setStats({
            documentsCount: 24,
            clientsCount: 8,
            pendingDocuments: 3,
          });
          setLoading(false);
        }, 1000);
      } catch (err: any) {
        console.error('Erro ao carregar dados do dashboard:', err);
        setError('Não foi possível carregar os dados do dashboard.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const recentActivities = [
    {
      id: 1,
      type: 'document',
      description: 'Extrato bancário processado com sucesso',
      date: '2023-06-15T14:30:00',
    },
    {
      id: 2,
      type: 'client',
      description: 'Novo cliente adicionado: Empresa XYZ',
      date: '2023-06-14T10:15:00',
    },
    {
      id: 3,
      type: 'notification',
      description: 'Lembrete: Atualizar documentos fiscais',
      date: '2023-06-13T09:00:00',
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <DescriptionIcon color="primary" />;
      case 'client':
        return <PersonIcon color="secondary" />;
      case 'notification':
        return <NotificationsIcon color="warning" />;
      default:
        return <DescriptionIcon />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
        <Button variant="contained" onClick={() => window.location.reload()} sx={{ mt: 2 }}>
          Tentar novamente
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Bem-vindo, {user?.name}!
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Confira o resumo das suas atividades e documentos
      </Typography>

      {/* Cards de estatísticas */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
            }}
          >
            <Typography variant="h6" gutterBottom>
              Documentos
            </Typography>
            <Typography variant="h3" sx={{ mt: 'auto' }}>
              {stats?.documentsCount || 0}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              bgcolor: 'secondary.light',
              color: 'secondary.contrastText',
            }}
          >
            <Typography variant="h6" gutterBottom>
              Clientes
            </Typography>
            <Typography variant="h3" sx={{ mt: 'auto' }}>
              {stats?.clientsCount || 0}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              height: 140,
              borderRadius: 2,
              bgcolor: 'warning.light',
              color: 'warning.contrastText',
            }}
          >
            <Typography variant="h6" gutterBottom>
              Pendentes
            </Typography>
            <Typography variant="h3" sx={{ mt: 'auto' }}>
              {stats?.pendingDocuments || 0}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Ações rápidas e atividades recentes */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Typography variant="h5" gutterBottom>
            Ações Rápidas
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <UploadIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Upload de Documentos</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Envie extratos bancários e outros documentos para processamento
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    color="primary"
                    onClick={() => navigate('/dashboard/documentos')}
                  >
                    Fazer Upload
                  </Button>
                </CardActions>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <BusinessIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Gerenciar Clientes</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Adicione ou edite informações dos seus clientes
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    color="primary"
                    onClick={() => navigate('/clientes')}
                  >
                    Ver Clientes
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, borderRadius: 2 }}>
            <Typography variant="h5" gutterBottom>
              Atividades Recentes
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <List>
              {recentActivities.map((activity) => (
                <ListItem key={activity.id} alignItems="flex-start">
                  <ListItemIcon>{getActivityIcon(activity.type)}</ListItemIcon>
                  <ListItemText
                    primary={activity.description}
                    secondary={formatDate(activity.date)}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard; 