import api from './api';

export interface AccountingRule {
  id: string;
  name: string;
  description?: string;
  pattern: string;
  accountCode: string;
  isDebit: boolean;
  isActive: boolean;
  priority: number;
  category?: string;
  tags?: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  company: string;
}

export interface AccountingRuleFilter {
  search?: string;
  isActive?: boolean;
  category?: string;
  page?: number;
  limit?: number;
}

export interface CreateAccountingRuleData {
  name: string;
  description?: string;
  pattern: string;
  accountCode: string;
  isDebit: boolean;
  isActive?: boolean;
  priority?: number;
  category?: string;
  tags?: string[];
}

export interface UpdateAccountingRuleData {
  name?: string;
  description?: string;
  pattern?: string;
  accountCode?: string;
  isDebit?: boolean;
  isActive?: boolean;
  priority?: number;
  category?: string;
  tags?: string[];
}

const accountingRuleService = {
  /**
   * Lista regras contábeis com filtros opcionais
   * @param filters Filtros para a listagem
   * @returns Promise com a lista de regras contábeis
   */
  async getRules(filters?: AccountingRuleFilter): Promise<{
    rules: AccountingRule[];
    total: number;
    page: number;
    limit: number;
  }> {
    const response = await api.get('/accounting-rules', { params: filters });
    return response.data;
  },

  /**
   * Obtém uma regra contábil pelo ID
   * @param id ID da regra contábil
   * @returns Promise com a regra contábil
   */
  async getRuleById(id: string): Promise<AccountingRule> {
    const response = await api.get(`/accounting-rules/${id}`);
    return response.data;
  },

  /**
   * Cria uma nova regra contábil
   * @param ruleData Dados da regra contábil
   * @returns Promise com a regra contábil criada
   */
  async createRule(ruleData: CreateAccountingRuleData): Promise<AccountingRule> {
    const response = await api.post('/accounting-rules', ruleData);
    return response.data;
  },

  /**
   * Atualiza uma regra contábil existente
   * @param id ID da regra contábil
   * @param ruleData Dados da regra contábil
   * @returns Promise com a regra contábil atualizada
   */
  async updateRule(id: string, ruleData: UpdateAccountingRuleData): Promise<AccountingRule> {
    const response = await api.put(`/accounting-rules/${id}`, ruleData);
    return response.data;
  },

  /**
   * Ativa ou desativa uma regra contábil
   * @param id ID da regra contábil
   * @param isActive Status de ativação
   * @returns Promise com a regra contábil atualizada
   */
  async toggleRuleStatus(id: string, isActive: boolean): Promise<AccountingRule> {
    const response = await api.patch(`/accounting-rules/${id}/status`, { isActive });
    return response.data;
  },

  /**
   * Exclui uma regra contábil
   * @param id ID da regra contábil
   * @returns Promise vazia em caso de sucesso
   */
  async deleteRule(id: string): Promise<void> {
    await api.delete(`/accounting-rules/${id}`);
  },

  /**
   * Obtém categorias de regras contábeis
   * @returns Promise com a lista de categorias
   */
  async getCategories(): Promise<string[]> {
    const response = await api.get('/accounting-rules/categories');
    return response.data;
  },

  /**
   * Testa uma regra contábil com um texto de exemplo
   * @param pattern Padrão da regra
   * @param sampleText Texto de exemplo
   * @returns Promise com o resultado do teste
   */
  async testRule(pattern: string, sampleText: string): Promise<{
    matches: boolean;
    matchedText?: string;
  }> {
    const response = await api.post('/accounting-rules/test', {
      pattern,
      sampleText
    });
    return response.data;
  },

  /**
   * Importa regras contábeis de um arquivo CSV ou Excel
   * @param file Arquivo com as regras
   * @returns Promise com as regras importadas
   */
  async importRules(file: File): Promise<{
    imported: number;
    errors: { row: number; message: string }[];
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/accounting-rules/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  },

  /**
   * Exporta regras contábeis para um arquivo CSV
   * @param filters Filtros para a exportação
   * @returns Promise com o blob do arquivo
   */
  async exportRules(filters?: AccountingRuleFilter): Promise<Blob> {
    const response = await api.get('/accounting-rules/export', {
      params: filters,
      responseType: 'blob'
    });
    
    return response.data;
  }
};

export default accountingRuleService; 