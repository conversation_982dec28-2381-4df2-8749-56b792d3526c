import React from 'react';
import { Box, Container, Grid, Typography, Avatar } from '@mui/material';

const teamMembers = [
  {
    name: '<PERSON>',
    role: '<PERSON><PERSON><PERSON>ênior',
    image: 'https://via.placeholder.com/150'
  },
  {
    name: '<PERSON>',
    role: 'Analista Contábil',
    image: 'https://via.placeholder.com/150'
  },
  {
    name: '<PERSON>',
    role: 'Especialista Fiscal',
    image: 'https://via.placeholder.com/150'
  }
];

const TeamSection: React.FC = () => {
  return (
    <Box sx={{ py: 8, bgcolor: 'background.default' }}>
      <Container maxWidth="lg">
        <Typography
          component="h2"
          variant="h3"
          align="center"
          color="text.primary"
          gutterBottom
        >
          Nossa Equipe
        </Typography>
        <Typography variant="h6" align="center" color="text.secondary" paragraph>
          Profissionais experientes e dedicados para atender suas necessidades contábeis
        </Typography>
        <Grid container spacing={4} justifyContent="center" sx={{ mt: 4 }}>
          {teamMembers.map((member) => (
            <Grid item key={member.name} xs={12} sm={6} md={4}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                }}
              >
                <Avatar
                  src={member.image}
                  alt={member.name}
                  sx={{ width: 150, height: 150, mb: 2 }}
                />
                <Typography variant="h6" gutterBottom>
                  {member.name}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {member.role}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default TeamSection; 