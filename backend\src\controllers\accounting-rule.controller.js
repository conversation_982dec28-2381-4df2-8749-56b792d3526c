const { AccountingRule, Company } = require('../models');
const { Op, Sequelize } = require('sequelize');
const sequelize = require('../models').sequelize;

/**
 * Obter todas as regras contábeis
 * @route GET /api/accounting-rules
 * @access Private
 */
exports.getRules = async (req, res) => {
  try {
    // Parâmetros de paginação e filtros
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Filtros
    const filters = {};

    // Filtro por empresa (se não for admin, filtrar pela empresa do usuário)
    if (req.user.role !== 'admin' && req.user.company_id) {
      filters.company_id = req.user.company_id;
    } else if (req.query.company_id) {
      filters.company_id = req.query.company_id;
    }

    // Filtro por status (ativo/inativo)
    if (req.query.is_active !== undefined) {
      filters.is_active = req.query.is_active === 'true';
    }

    // Filtro por categoria
    if (req.query.category) {
      filters.category = req.query.category;
    }

    // Filtro por busca (nome ou descrição)
    if (req.query.search) {
      filters[Op.or] = [
        { name: { [Op.like]: `%${req.query.search}%` } },
        { description: { [Op.like]: `%${req.query.search}%` } }
      ];
    }

    // Buscar regras contábeis
    const { count, rows: rules } = await AccountingRule.findAndCountAll({
      where: filters,
      include: [
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    // Calcular total de páginas
    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        rules,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('Erro ao obter regras contábeis:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter regras contábeis',
      error: error.message
    });
  }
};

/**
 * Obter uma regra contábil pelo ID
 * @route GET /api/accounting-rules/:id
 * @access Private
 */
exports.getRuleById = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar regra contábil
    const rule = await AccountingRule.findByPk(id, {
      include: [
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ]
    });

    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Regra contábil não encontrada'
      });
    }

    // Verificar se o usuário tem acesso à regra
    if (req.user.role !== 'admin' && rule.company_id && req.user.company_id !== rule.company_id) {
      return res.status(403).json({
        success: false,
        message: 'Acesso não autorizado a esta regra contábil'
      });
    }

    res.json({
      success: true,
      data: rule
    });
  } catch (error) {
    console.error('Erro ao obter regra contábil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter regra contábil',
      error: error.message
    });
  }
};

/**
 * Criar uma nova regra contábil
 * @route POST /api/accounting-rules
 * @access Private/Admin/Accountant
 */
exports.createRule = async (req, res) => {
  try {
    const {
      name,
      description,
      rule_type,
      conditions,
      actions,
      company_id,
      is_active
    } = req.body;

    // Definir a empresa da regra
    let ruleCompanyId = company_id;

    // Se não for admin, usar a empresa do usuário
    if (req.user.role !== 'admin') {
      ruleCompanyId = req.user.company_id;
    }

    // Criar regra contábil
    const rule = await AccountingRule.create({
      name,
      description,
      rule_type,
      conditions,
      actions,
      company_id: ruleCompanyId,
      is_active: is_active !== undefined ? is_active : true
    });

    res.status(201).json({
      success: true,
      data: rule
    });
  } catch (error) {
    console.error('Erro ao criar regra contábil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao criar regra contábil',
      error: error.message
    });
  }
};

/**
 * Atualizar uma regra contábil
 * @route PUT /api/accounting-rules/:id
 * @access Private/Admin/Accountant
 */
exports.updateRule = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      rule_type,
      conditions,
      actions,
      is_active
    } = req.body;

    // Buscar regra contábil
    const rule = await AccountingRule.findByPk(id);

    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Regra contábil não encontrada'
      });
    }

    // Verificar se o usuário tem acesso à regra
    if (req.user.role !== 'admin' && rule.company_id && req.user.company_id !== rule.company_id) {
      return res.status(403).json({
        success: false,
        message: 'Acesso não autorizado a esta regra contábil'
      });
    }

    // Atualizar regra contábil
    await rule.update({
      name,
      description,
      rule_type,
      conditions,
      actions,
      is_active
    });

    res.json({
      success: true,
      data: rule
    });
  } catch (error) {
    console.error('Erro ao atualizar regra contábil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao atualizar regra contábil',
      error: error.message
    });
  }
};

/**
 * Excluir uma regra contábil
 * @route DELETE /api/accounting-rules/:id
 * @access Private/Admin/Accountant
 */
exports.deleteRule = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar regra contábil
    const rule = await AccountingRule.findByPk(id);

    if (!rule) {
      return res.status(404).json({
        success: false,
        message: 'Regra contábil não encontrada'
      });
    }

    // Verificar se o usuário tem acesso à regra
    if (req.user.role !== 'admin' && rule.company_id && req.user.company_id !== rule.company_id) {
      return res.status(403).json({
        success: false,
        message: 'Acesso não autorizado a esta regra contábil'
      });
    }

    // Excluir regra contábil
    await rule.destroy();

    res.json({
      success: true,
      message: 'Regra contábil excluída com sucesso'
    });
  } catch (error) {
    console.error('Erro ao excluir regra contábil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao excluir regra contábil',
      error: error.message
    });
  }
};

/**
 * Obter categorias de regras contábeis
 * @route GET /api/accounting-rules/categories
 * @access Private
 */
exports.getCategories = async (req, res) => {
  try {
    // Buscar categorias distintas
    const categories = await AccountingRule.findAll({
      attributes: [[sequelize.fn('DISTINCT', sequelize.col('rule_type')), 'rule_type']],
      raw: true
    });

    res.json({
      success: true,
      data: categories.map(c => c.rule_type)
    });
  } catch (error) {
    console.error('Erro ao obter categorias:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter categorias',
      error: error.message
    });
  }
};

/**
 * Testar uma regra contábil
 * @route POST /api/accounting-rules/test
 * @access Private
 */
exports.testRule = async (req, res) => {
  try {
    const { pattern, sampleText } = req.body;

    if (!pattern || !sampleText) {
      return res.status(400).json({
        success: false,
        message: 'Padrão e texto de exemplo são obrigatórios'
      });
    }

    // Testar o padrão
    let regex;
    try {
      regex = new RegExp(pattern);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: 'Padrão de expressão regular inválido',
        error: error.message
      });
    }

    const matches = regex.test(sampleText);
    const matchedText = matches ? sampleText.match(regex)[0] : null;

    res.json({
      success: true,
      data: {
        matches,
        matchedText
      }
    });
  } catch (error) {
    console.error('Erro ao testar regra:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao testar regra',
      error: error.message
    });
  }
};

/**
 * Importar regras contábeis
 * @route POST /api/accounting-rules/import
 * @access Private/Admin/Accountant
 */
exports.importRules = async (req, res) => {
  // Implementação simplificada para MVP
  res.json({
    success: true,
    data: {
      imported: 0,
      errors: []
    },
    message: 'Funcionalidade de importação será implementada em versões futuras'
  });
};

/**
 * Exportar regras contábeis
 * @route GET /api/accounting-rules/export
 * @access Private/Admin/Accountant
 */
exports.exportRules = async (req, res) => {
  // Implementação simplificada para MVP
  res.json({
    success: true,
    message: 'Funcionalidade de exportação será implementada em versões futuras'
  });
};
