import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Grid,
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const NotFound = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={6} sx={{ textAlign: { xs: 'center', md: 'right' } }}>
            <ErrorOutlineIcon sx={{ fontSize: 120, color: 'text.secondary' }} />
          </Grid>
          <Grid item xs={12} md={6} sx={{ textAlign: { xs: 'center', md: 'left' } }}>
            <Typography variant="h1" component="h1" sx={{ fontWeight: 700 }}>
              404
            </Typography>
            <Typography variant="h5" component="h2" gutterBottom>
              Página não encontrada
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              A página que você está procurando não existe ou foi movida.
            </Typography>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            Voltar para Home
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate(-1)}
          >
            Voltar
          </Button>
        </Box>
      </Paper>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Precisa de ajuda? Entre em contato com nosso suporte em{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Typography>
      </Box>
    </Container>
  );
};

export default NotFound; 