const { Model, DataTypes } = require('sequelize');

class Company extends Model {
  static init(sequelize) {
    super.init({
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      document_type: {
        type: DataTypes.ENUM('cnpj', 'cpf'),
        allowNull: false
      },
      document_number: {
        type: DataTypes.STRING(14),
        allowNull: false,
        unique: true,
        validate: {
          notEmpty: true,
          len: [11, 14]
        }
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          isEmail: true
        }
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true
      },
      address_street: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_number: {
        type: DataTypes.STRING(20),
        allowNull: true
      },
      address_complement: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_neighborhood: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_city: {
        type: DataTypes.STRING,
        allowNull: true
      },
      address_state: {
        type: DataTypes.CHAR(2),
        allowNull: true
      },
      address_zip_code: {
        type: DataTypes.STRING(8),
        allowNull: true
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      }
    }, {
      sequelize,
      tableName: 'companies'
    });
  }

  static associate(models) {
    this.hasMany(models.User, { foreignKey: 'company_id', as: 'users' });
    this.hasMany(models.Document, { foreignKey: 'company_id', as: 'documents' });
    this.hasMany(models.AccountingRule, { foreignKey: 'company_id', as: 'accounting_rules' });
  }
}

module.exports = Company; 