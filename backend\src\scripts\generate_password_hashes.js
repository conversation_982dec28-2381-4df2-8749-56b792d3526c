const bcrypt = require('bcrypt');
const fs = require('fs');
const path = require('path');

async function generateHashes() {
  const saltRounds = 10;
  
  // Gerar <NAME_EMAIL>
  const adminPassword = 'Admin@123';
  const adminHash = await bcrypt.hash(adminPassword, saltRounds);
  
  // Gerar <NAME_EMAIL>
  const contadorPassword = 'Contador@123';
  const contadorHash = await bcrypt.hash(contadorPassword, saltRounds);
  
  const sqlContent = `USE mvp_contabil;

-- Senha: Admin@123
UPDATE users 
SET password = '${adminHash}'
WHERE email = '<EMAIL>';

-- Senha: Contador@123
UPDATE users 
SET password = '${contadorHash}'
WHERE email = '<EMAIL>';`;

  // <PERSON>var o SQL em um arquivo
  const sqlPath = path.join(__dirname, 'update_passwords.sql');
  fs.writeFileSync(sqlPath, sqlContent);
  
  console.log('Hashes gerados:');
  console.log('\nAdmin:', adminHash);
  console.log('Contador:', contadorHash);
  console.log('\nArquivo SQL gerado em:', sqlPath);
}

generateHashes().catch(console.error); 