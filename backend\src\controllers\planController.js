const Plan = require('../models/Plan');

// Obter todos os planos
exports.getAllPlans = async (req, res) => {
  try {
    const plans = await Plan.find();
    
    return res.status(200).json({
      success: true,
      count: plans.length,
      data: plans
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Erro ao obter planos',
      error: error.message
    });
  }
};

// Obter um plano específico pelo ID
exports.getPlanById = async (req, res) => {
  try {
    const plan = await Plan.findById(req.params.id);
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plano não encontrado'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: plan
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Erro ao obter plano',
      error: error.message
    });
  }
};

// Criar um novo plano
exports.createPlan = async (req, res) => {
  try {
    // Verificar se o usuário é admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado. Apenas administradores podem criar planos.'
      });
    }
    
    const plan = await Plan.create(req.body);
    
    return res.status(201).json({
      success: true,
      data: plan
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        message: 'Erro de validação',
        errors: messages
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Erro ao criar plano',
      error: error.message
    });
  }
};

// Atualizar um plano existente
exports.updatePlan = async (req, res) => {
  try {
    // Verificar se o usuário é admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado. Apenas administradores podem atualizar planos.'
      });
    }
    
    const plan = await Plan.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plano não encontrado'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: plan
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        message: 'Erro de validação',
        errors: messages
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Erro ao atualizar plano',
      error: error.message
    });
  }
};

// Desativar um plano (soft delete)
exports.deletePlan = async (req, res) => {
  try {
    // Verificar se o usuário é admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado. Apenas administradores podem desativar planos.'
      });
    }
    
    // Ao invés de excluir, apenas desativa o plano
    const plan = await Plan.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plano não encontrado'
      });
    }
    
    return res.status(200).json({
      success: true,
      message: 'Plano desativado com sucesso',
      data: plan
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Erro ao desativar plano',
      error: error.message
    });
  }
}; 