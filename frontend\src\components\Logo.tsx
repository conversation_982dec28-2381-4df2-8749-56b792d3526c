import React from 'react';
import { Box, Typography } from '@mui/material';

interface LogoProps {
  variant?: 'light' | 'dark';
}

const Logo: React.FC<LogoProps> = ({ variant = 'dark' }) => {
  const color = variant === 'light' ? '#fff' : '#1976d2';
  const [imageError, setImageError] = React.useState(false);

  // Lista de caminhos possíveis para a logo
  const logoPaths = [
    '/static/media/LOGOSPNG/logo.png',
    '../static/media/LOGOSPNG/logo.png',
    '../../static/media/LOGOSPNG/logo.png',
    '/logo.png',
    process.env.PUBLIC_URL + '/static/media/LOGOSPNG/logo.png'
  ];

  // Tenta carregar a imagem de diferentes caminhos
  const [currentPathIndex, setCurrentPathIndex] = React.useState(0);

  const handleImageError = () => {
    if (currentPathIndex < logoPaths.length - 1) {
      setCurrentPathIndex(currentPathIndex + 1);
    } else {
      setImageError(true);
    }
  };

  // Fallback para texto quando a imagem não pode ser carregada
  if (imageError) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography
          variant="h5"
          component="span"
          sx={{
            fontWeight: 700,
            color: color,
            letterSpacing: 1,
          }}
        >
          MVP
        </Typography>
        <Typography
          variant="h5"
          component="span"
          sx={{
            fontWeight: 400,
            color: color,
            ml: 0.5,
          }}
        >
          Contábil
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <img 
        src={logoPaths[currentPathIndex]}
        alt="MVP Contábil" 
        style={{ 
          height: '40px',
          width: 'auto'
        }}
        onError={handleImageError}
      />
    </Box>
  );
};

export default Logo; 