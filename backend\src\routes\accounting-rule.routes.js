const express = require('express');
const router = express.Router();
const accountingRuleController = require('../controllers/accounting-rule.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');
const upload = require('../middlewares/upload');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Obter todas as regras contábeis
router.get('/', accountingRuleController.getRules);

// Obter categorias de regras contábeis
router.get('/categories', accountingRuleController.getCategories);

// Testar uma regra contábil
router.post('/test', accountingRuleController.testRule);

// Importar regras contábeis (apenas admin e accountant)
router.post('/import', 
  authorize(['admin', 'accountant']), 
  upload.single('file'), 
  accountingRuleController.importRules
);

// Exportar regras contábeis (apenas admin e accountant)
router.get('/export', 
  authorize(['admin', 'accountant']), 
  accountingRuleController.exportRules
);

// Obter uma regra contábil pelo ID
router.get('/:id', accountingRuleController.getRuleById);

// Criar uma nova regra contábil (apenas admin e accountant)
router.post('/', 
  authorize(['admin', 'accountant']), 
  accountingRuleController.createRule
);

// Atualizar uma regra contábil (apenas admin e accountant)
router.put('/:id', 
  authorize(['admin', 'accountant']), 
  accountingRuleController.updateRule
);

// Excluir uma regra contábil (apenas admin e accountant)
router.delete('/:id', 
  authorize(['admin', 'accountant']), 
  accountingRuleController.deleteRule
);

module.exports = router;
