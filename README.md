# <div align="center">🏢 Nexlify Contábil</div>

<div align="center">
  <img src="frontend/public/static/media/LOGOSPNG/logo.png" alt="Nexlify Logo" width="200"/>
  
  <p>
    <strong>Sistema de Gestão de Documentos e Processamento Contábil</strong>
  </p>

  <p>
    <img src="https://img.shields.io/badge/Status-Em%20Desenvolvimento-yellow"/>
    <img src="https://img.shields.io/badge/Versão-1.0.0-blue"/>
    <img src="https://img.shields.io/badge/Licença-MIT-green"/>
  </p>
</div>

---

## 📋 Sobre o Projeto

O Nexlify Contábil é uma plataforma moderna e eficiente para gestão de documentos e processamento contábil, desenvolvida para atender às necessidades de escritórios contábeis e profissionais autônomos.

### 🌟 Principais Funcionalidades

<table>
  <tr>
    <td>✨ Upload e gerenciamento de documentos</td>
    <td>📊 Dashboard intuitivo</td>
  </tr>
  <tr>
    <td>👥 Gestão de clientes</td>
    <td>📑 Processamento automático de documentos</td>
  </tr>
  <tr>
    <td>🔒 Controle de acesso e permissões</td>
    <td>📈 Relatórios personalizados</td>
  </tr>
  <tr>
    <td>💼 Múltiplos planos de assinatura</td>
    <td>🔄 Integração com sistemas contábeis</td>
  </tr>
</table>

## 🚀 Stack Tecnológica

### Frontend
```json
{
  "principal": {
    "framework": "React.js",
    "linguagem": "TypeScript",
    "ui": "Material-UI"
  },
  "ferramentas": {
    "gerenciamentoEstado": "Context API",
    "requisições": "Axios",
    "rotas": "React Router DOM"
  }
}
```

### Backend
```json
{
  "principal": {
    "runtime": "Node.js",
    "framework": "Express",
    "banco": "MySQL 8",
    "autenticação": "JWT"
  },
  "ferramentas": {
    "upload": "Multer",
    "validação": "Joi",
    "logs": "Winston"
  }
}
```

## 💻 Pré-requisitos

- Node.js (v14 ou superior)
- MySQL 8
- NPM ou Yarn

## 🛠️ Guia de Instalação

### 1️⃣ Backend

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/nexlify-contabil.git

# Entre na pasta do backend
cd backend

# Instale as dependências
npm install

# Configure o ambiente
cp .env.example .env

# Configure o banco de dados
mysql -u root -p < src/database/schema.sql

# Inicie o servidor
npm run dev
```

### 2️⃣ Frontend

```bash
# Entre na pasta do frontend
cd frontend

# Instale as dependências
npm install

# Configure o ambiente
cp .env.example .env

# Inicie a aplicação
npm start
```

## 📱 Funcionalidades por Perfil

<table>
  <tr>
    <th>👔 Administrador</th>
    <th>📚 Contador</th>
    <th>👤 Cliente</th>
  </tr>
  <tr>
    <td>
      • Gestão de usuários<br/>
      • Configuração de regras<br/>
      • Relatórios avançados<br/>
      • Gestão de planos
    </td>
    <td>
      • Processamento de docs<br/>
      • Gestão de clientes<br/>
      • Config. de regras<br/>
      • Relatórios básicos
    </td>
    <td>
      • Upload de documentos<br/>
      • Visualização de status<br/>
      • Acesso a relatórios<br/>
      • Chat com contador
    </td>
  </tr>
</table>

## 🔐 Planos e Recursos

<table>
  <tr>
    <th>Individual</th>
    <th>Empresarial Básico</th>
    <th>Empresarial Plus</th>
    <th>Premium</th>
  </tr>
  <tr>
    <td>
      • 1 usuário<br/>
      • 100 docs/mês<br/>
      • 5GB storage<br/>
      • Suporte email
    </td>
    <td>
      • 5 usuários<br/>
      • 1.000 docs/mês<br/>
      • 20GB storage<br/>
      • Suporte chat
    </td>
    <td>
      • 10 usuários<br/>
      • 3.000 docs/mês<br/>
      • 50GB storage<br/>
      • Suporte prioritário
    </td>
    <td>
      • Ilimitado<br/>
      • Ilimitado<br/>
      • 100GB storage<br/>
      • Suporte VIP 24/7
    </td>
  </tr>
</table>

## 🌐 Estrutura do Projeto

```
nexlify-contabil/
├── backend/
│   ├── src/
│   │   ├── config/      # Configurações
│   │   ├── controllers/ # Controladores
│   │   ├── database/    # Migrations e seeds
│   │   ├── middlewares/ # Middlewares
│   │   ├── models/      # Modelos
│   │   ├── routes/      # Rotas
│   │   └── services/    # Regras de negócio
│   └── package.json
│
└── frontend/
    ├── public/          # Arquivos estáticos
    ├── src/
    │   ├── components/  # Componentes React
    │   ├── contexts/    # Contextos
    │   ├── pages/       # Páginas
    │   ├── services/    # Serviços
    │   └── utils/       # Utilitários
    └── package.json
```

## 📄 Licença

Este projeto está sob a licença MIT. Consulte o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👥 Como Contribuir

1. Fork o projeto
2. Crie sua feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add: nova funcionalidade'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte e Contato

<table>
  <tr>
    <td>📧 Email:</td>
    <td><EMAIL></td>
  </tr>
  <tr>
    <td>📱 Telefone:</td>
    <td>(11) 9999-9999</td>
  </tr>
  <tr>
    <td>🌐 Website:</td>
    <td>www.nexlify.com</td>
  </tr>
</table>

---

<div align="center">
  <p>Desenvolvido com ❤️ por Nexlify</p>
  <p>
    <small>© 2024 Nexlify Contábil. Todos os direitos reservados.</small>
  </p>
</div>
