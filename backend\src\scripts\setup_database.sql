-- Criar banco de dados
CREATE DATABASE IF NOT EXISTS mvp_contabil;
USE mvp_contabil;

-- <PERSON>riar tabela de usuários
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'accountant') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Criar tabela de empresas
CREATE TABLE IF NOT EXISTS companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    cnpj VARCHAR(14) NOT NULL UNIQUE,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Criar tabela de clientes
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    cpf VARCHAR(11) NOT NULL UNIQUE,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    company_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Criar tabela de planos contábeis
CREATE TABLE IF NOT EXISTS plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    type ENUM('debit', 'credit') NOT NULL,
    parent_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_code) REFERENCES plans(code)
);

-- Criar tabela de transações
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    description TEXT,
    value DECIMAL(10,2) NOT NULL,
    type ENUM('debit', 'credit') NOT NULL,
    company_id INT NOT NULL,
    plan_code VARCHAR(20) NOT NULL,
    document VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (plan_code) REFERENCES plans(code)
);

-- Criar tabela de documentos
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    number VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    company_id INT NOT NULL,
    client_id INT NOT NULL,
    status ENUM('pending', 'processed') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id)
);

-- Inserir dados de teste

-- Inserir usuários
INSERT INTO users (name, email, password, role) VALUES
('Administrador', '<EMAIL>', '$2a$10$X7VqWGzKyYqIxRgqgUZ6A.9X9qY4jA6XZ6Z1Z6Z1Z6Z1Z6Z1Z6', 'admin'),
('Contador', '<EMAIL>', '$2a$10$X7VqWGzKyYqIxRgqgUZ6A.9X9qY4jA6XZ6Z1Z6Z1Z6Z1Z6Z1Z6', 'accountant');

-- Inserir empresas
INSERT INTO companies (name, cnpj, address, phone, email) VALUES
('Empresa ABC Ltda', '***********234', 'Rua Principal, 123', '(11) 1234-5678', '<EMAIL>'),
('XYZ Comércio S.A.', '***********876', 'Avenida Comercial, 456', '(11) 9876-5432', '<EMAIL>');

-- Inserir clientes
INSERT INTO clients (name, cpf, address, phone, email, company_id) VALUES
('João Silva', '***********', 'Rua dos Clientes, 789', '(11) 98765-4321', '<EMAIL>', 1),
('Maria Santos', '***********', 'Avenida dos Compradores, 321', '(11) 91234-5678', '<EMAIL>', 2);

-- Inserir planos contábeis
INSERT INTO plans (code, name, type) VALUES
('1', 'Ativo', 'debit'),
('2', 'Passivo', 'credit');

INSERT INTO plans (code, name, type, parent_code) VALUES
('1.1', 'Ativo Circulante', 'debit', '1'),
('2.1', 'Passivo Circulante', 'credit', '2');

-- Inserir transações
INSERT INTO transactions (date, description, value, type, company_id, plan_code, document) VALUES
(CURRENT_DATE, 'Venda à Vista', 1000.00, 'credit', 1, '1.1', 'NF-001'),
(CURRENT_DATE, 'Pagamento Fornecedor', 500.00, 'debit', 2, '2.1', 'NF-002');

-- Inserir documentos
INSERT INTO documents (number, type, date, value, company_id, client_id, status) VALUES
('NF-001', 'invoice', CURRENT_DATE, 1000.00, 1, 1, 'processed'),
('NF-002', 'invoice', CURRENT_DATE, 500.00, 2, 2, 'pending'); 