const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Criar plano inicial
    const planId = uuidv4();
    await queryInterface.bulkInsert('plans', [{
      id: planId,
      name: 'Plano Básico',
      description: 'Plano básico para começar',
      price: 99.90,
      additional_client_price: 29.90,
      max_users: 5,
      max_clients: 10,
      max_documents_per_month: 100,
      max_transactions_per_month: 1000,
      storage_limit: 5120, // 5GB em MB
      features: JSON.stringify(['upload_documentos', 'processamento_basico']),
      plan_type: 'business_basic',
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }]);

    // Criar empresa inicial
    const companyId = uuidv4();
    await queryInterface.bulkInsert('companies', [{
      id: companyId,
      name: 'Emp<PERSON>a Exemplo',
      document_type: 'cnpj',
      document_number: '12345678000199',
      email: '<EMAIL>',
      phone: '***********',
      address_street: 'Rua Exemplo',
      address_number: '123',
      address_complement: 'Sala 1',
      address_neighborhood: 'Centro',
      address_city: 'São Paulo',
      address_state: 'SP',
      address_zip_code: '01001000',
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }]);

    // Criar usuário admin inicial
    const hashedPassword = await bcrypt.hash('Admin@123', 10);
    await queryInterface.bulkInsert('users', [{
      id: uuidv4(),
      name: 'Administrador',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      company_id: companyId,
      plan_id: planId,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('companies', null, {});
    await queryInterface.bulkDelete('plans', null, {});
  }
}; 