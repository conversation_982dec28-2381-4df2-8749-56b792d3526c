const express = require('express');
const router = express.Router();
const planController = require('../controllers/plan.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');

// Rotas públicas
router.get('/public', planController.getPublicPlans);
router.get('/', planController.getPlans);
router.get('/:id', planController.getPlanById);

// Rotas protegidas (apenas admin)
router.post('/', authenticate, authorize(['admin']), planController.createPlan);
router.put('/:id', authenticate, authorize(['admin']), planController.updatePlan);
router.delete('/:id', authenticate, authorize(['admin']), planController.deletePlan);

module.exports = router; 