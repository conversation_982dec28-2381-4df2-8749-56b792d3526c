import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import api from '../services/api';

// Tipos
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  company?: any;
  plan?: any;
  lastLogin?: Date;
}

interface AuthContextData {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, planId?: string) => Promise<void>;
  logout: () => void;
  error: string | null;
  clearError: () => void;
  updateUser: (user: User) => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
}

interface AuthProviderProps {
  children: ReactNode;
}

// Criação do contexto
const AuthContext = createContext<AuthContextData>({} as AuthContextData);

// Hook personalizado para usar o contexto
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

// Provider do contexto
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStoredUser = async () => {
    try {
      const storedToken = localStorage.getItem('@NexlifyContabil:token');
      const storedRefreshToken = localStorage.getItem('@NexlifyContabil:refreshToken');
      const storedUser = localStorage.getItem('@NexlifyContabil:user');

      if (storedToken && storedUser) {
        api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;

        try {
          const response = await api.get('/auth/me');
          const updatedUser = response.data.data;

          // Atualiza o usuário com os dados mais recentes do servidor
          localStorage.setItem('@NexlifyContabil:user', JSON.stringify(updatedUser));
          setUser(updatedUser);
        } catch (error: any) {
          // O interceptor do axios já vai tentar fazer o refresh token
          // Se mesmo assim falhar, o logout será feito automaticamente
          console.error('Erro ao carregar usuário:', error);
        }
      } else if (storedRefreshToken) {
        // Se não tiver token de acesso mas tiver refresh token, tentar renovar
        try {
          const response = await api.post('/auth/refresh-token', { refreshToken: storedRefreshToken });
          const { token, refreshToken } = response.data.data;

          localStorage.setItem('@NexlifyContabil:token', token);
          localStorage.setItem('@NexlifyContabil:refreshToken', refreshToken);

          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          // Buscar dados do usuário
          const userResponse = await api.get('/auth/me');
          const userData = userResponse.data.data;

          localStorage.setItem('@NexlifyContabil:user', JSON.stringify(userData));
          setUser(userData);
        } catch (error) {
          console.error('Erro ao renovar token:', error);
          logout();
        }
      } else {
        // Se não tiver nem token nem refresh token, fazer logout
        logout();
      }
    } catch (error) {
      console.error('Erro ao carregar usuário:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  // Verificar se o usuário está autenticado ao carregar a página
  useEffect(() => {
    loadStoredUser();
  }, []);

  // Atualizar o token periodicamente
  useEffect(() => {
    if (user) {
      const refreshInterval = setInterval(loadStoredUser, 5 * 60 * 1000); // A cada 5 minutos
      return () => clearInterval(refreshInterval);
    }
  }, [user]);

  // Login
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fazendo requisição de login...');
      const response = await api.post('/auth/login', { email, password });
      const { token, refreshToken, user: userData } = response.data.data;

      console.log('Login bem-sucedido, salvando dados...', { token: !!token, refreshToken: !!refreshToken, user: userData });
      localStorage.setItem('@NexlifyContabil:token', token);
      localStorage.setItem('@NexlifyContabil:refreshToken', refreshToken);
      localStorage.setItem('@NexlifyContabil:user', JSON.stringify(userData));

      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      setUser(userData);
      console.log('Estado de autenticação atualizado');
    } catch (err: any) {
      console.error('Erro durante o login:', err);
      const errorMessage = err.response?.data?.message || 'Erro ao fazer login. Tente novamente.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Registro
  const register = async (name: string, email: string, password: string, planId?: string) => {
    try {
      setLoading(true);
      setError(null);

      const userData = planId
        ? { name, email, password, planId }
        : { name, email, password };

      const response = await api.post('/auth/register', userData);
      const { token, refreshToken, user } = response.data.data;

      localStorage.setItem('@NexlifyContabil:token', token);
      localStorage.setItem('@NexlifyContabil:refreshToken', refreshToken);
      localStorage.setItem('@NexlifyContabil:user', JSON.stringify(user));

      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      setUser(user);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erro ao criar conta. Tente novamente.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Logout
  const logout = () => {
    localStorage.removeItem('@NexlifyContabil:token');
    localStorage.removeItem('@NexlifyContabil:refreshToken');
    localStorage.removeItem('@NexlifyContabil:user');
    api.defaults.headers.common['Authorization'] = '';
    setUser(null);
  };

  // Atualizar dados do usuário
  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
    localStorage.setItem('@NexlifyContabil:user', JSON.stringify(updatedUser));
  };

  // Esqueci a senha
  const forgotPassword = async (email: string) => {
    try {
      await api.post('/auth/forgot-password', { email });
    } catch (error) {
      console.error('Erro ao solicitar redefinição de senha:', error);
      throw error;
    }
  };

  // Redefinir senha
  const resetPassword = async (token: string, password: string) => {
    try {
      await api.post(`/auth/reset-password/${token}`, { password });
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      throw error;
    }
  };

  // Valor do contexto
  const value = {
    user,
    isAuthenticated: !!user,
    loading,
    login,
    register,
    logout,
    updateUser,
    forgotPassword,
    resetPassword,
    error,
    clearError: () => setError(null)
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};