const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const { authenticate } = require('../middlewares/auth.middleware');

// Rota para registro de usuário
router.post('/register', authController.register);

// Rota para login
router.post('/login', authController.login);

// Rota para obter dados do usuário atual (protegida)
router.get('/me', authenticate, authController.getMe);

// Rota para solicitar redefinição de senha
router.post('/forgot-password', authController.forgotPassword);

// Rota para redefinir senha
router.post('/reset-password/:token', authController.resetPassword);

// Rota para atualizar token usando refresh token
router.post('/refresh-token', authController.refreshToken);

module.exports = router;