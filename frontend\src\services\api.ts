import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar o token de autenticação
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('@NexlifyContabil:token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Função para atualizar o token
const refreshAccessToken = async () => {
  try {
    const refreshToken = localStorage.getItem('@NexlifyContabil:refreshToken');

    if (!refreshToken) {
      throw new Error('Refresh token não encontrado');
    }

    const response = await axios.post(
      `${process.env.REACT_APP_API_URL || 'http://localhost:3000/api'}/auth/refresh-token`,
      { refreshToken }
    );

    const { token, refreshToken: newRefreshToken } = response.data.data;

    localStorage.setItem('@NexlifyContabil:token', token);
    localStorage.setItem('@NexlifyContabil:refreshToken', newRefreshToken);

    return token;
  } catch (error) {
    // Se não conseguir atualizar o token, fazer logout
    localStorage.removeItem('@NexlifyContabil:token');
    localStorage.removeItem('@NexlifyContabil:refreshToken');
    localStorage.removeItem('@NexlifyContabil:user');
    throw error;
  }
};

// Interceptor para tratamento de erros
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Se for erro 401 (não autorizado) e não for uma tentativa de refresh token
    if (error.response?.status === 401 &&
        !originalRequest._retry &&
        !originalRequest.url.includes('auth/refresh-token')) {

      originalRequest._retry = true;

      try {
        // Tentar atualizar o token
        const newToken = await refreshAccessToken();

        // Atualizar o header da requisição original
        originalRequest.headers.Authorization = `Bearer ${newToken}`;

        // Repetir a requisição original com o novo token
        return api(originalRequest);
      } catch (refreshError) {
        // Se não conseguir atualizar o token, tratar o erro
        error.message = 'Sessão expirada. Por favor, faça login novamente.';
        window.location.href = '/login'; // Redirecionar para a página de login
        return Promise.reject(error);
      }
    }

    // Para outros erros, tratar normalmente
    if (error.response) {
      // Erro do servidor com resposta
      switch (error.response.status) {
        case 401:
          // Erro de autenticação
          localStorage.removeItem('@NexlifyContabil:token');
          localStorage.removeItem('@NexlifyContabil:refreshToken');
          localStorage.removeItem('@NexlifyContabil:user');
          error.message = 'Sessão expirada. Por favor, faça login novamente.';
          break;
        case 403:
          // Erro de permissão
          error.message = 'Você não tem permissão para acessar este recurso.';
          break;
        case 404:
          // Recurso não encontrado
          error.message = 'O recurso solicitado não foi encontrado.';
          break;
        case 500:
          // Erro interno do servidor
          error.message = 'Erro interno do servidor. Tente novamente mais tarde.';
          break;
        default:
          error.message = error.response.data?.message || 'Ocorreu um erro. Tente novamente.';
      }
    } else if (error.request) {
      // Erro de conexão
      error.message = 'Não foi possível conectar ao servidor. Verifique sua conexão.';
    } else {
      // Erro na configuração da requisição
      error.message = 'Erro ao processar sua requisição. Tente novamente.';
    }

    return Promise.reject(error);
  }
);

// Serviços de autenticação
export const authService = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },
  register: async (userData: any) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },
  forgotPassword: async (email: string) => {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  },
  resetPassword: async (token: string, password: string) => {
    const response = await api.post('/auth/reset-password', { token, password });
    return response.data;
  },
};

// Serviços de usuário
export const userService = {
  getProfile: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },
  updateProfile: async (userData: any) => {
    const response = await api.put('/users/me', userData);
    return response.data;
  },
  changePassword: async (currentPassword: string, newPassword: string) => {
    const response = await api.put('/users/change-password', {
      currentPassword,
      newPassword,
    });
    return response.data;
  },
};

// Serviços de planos
export const planService = {
  getPublicPlans: async () => {
    try {
      const response = await api.get('/plans/public');
      return response.data;
    } catch (error) {
      console.error('Erro ao obter planos:', error);
      // Retornar dados mockados em caso de erro
      return {
        success: true,
        data: [
          {
            _id: 'plan_1',
            id: 'plan_1',
            name: 'Plano Individual',
            description: 'Ideal para profissionais autônomos que precisam gerenciar seus documentos contábeis.',
            price: 750,
            additionalClientPrice: 0,
            maxUsers: 1,
            maxClients: 1,
            maxDocumentsPerMonth: 100,
            maxTransactionsPerMonth: 500,
            storageLimit: 5,
            features: [
              'Suporte por e-mail',
              'Upload de documentos',
              'Processamento de extratos bancários',
              'Geração de arquivos para importação',
              'Dashboard básico'
            ],
            planType: 'individual',
            isActive: true
          },
          {
            _id: 'plan_2',
            id: 'plan_2',
            name: 'Plano Empresarial Básico',
            description: 'Perfeito para pequenas empresas contábeis com até 5 usuários e 500 clientes.',
            price: 2000,
            additionalClientPrice: 0,
            maxUsers: 5,
            maxClients: 500,
            maxDocumentsPerMonth: 1000,
            maxTransactionsPerMonth: 5000,
            storageLimit: 20,
            features: [
              'Suporte por e-mail e chat',
              'Upload de documentos',
              'Processamento de extratos bancários',
              'Geração de arquivos para importação',
              'Dashboard completo',
              'Gestão de clientes',
              'Relatórios básicos'
            ],
            planType: 'business_basic',
            isActive: true
          },
          {
            _id: 'plan_3',
            id: 'plan_3',
            name: 'Plano Empresarial Plus',
            description: 'Ideal para empresas contábeis em crescimento com até 10 usuários e 1.000 clientes.',
            price: 3500,
            additionalClientPrice: 0,
            maxUsers: 10,
            maxClients: 1000,
            maxDocumentsPerMonth: 3000,
            maxTransactionsPerMonth: 15000,
            storageLimit: 50,
            features: [
              'Suporte prioritário por e-mail, chat e telefone',
              'Upload de documentos',
              'Processamento de extratos bancários',
              'Geração de arquivos para importação',
              'Dashboard completo',
              'Gestão de clientes',
              'Relatórios avançados',
              'API básica para integração',
              'Múltiplos layouts de exportação'
            ],
            planType: 'business_plus',
            isActive: true
          },
          {
            _id: 'plan_4',
            id: 'plan_4',
            name: 'Plano Empresarial Premium',
            description: 'Solução completa para grandes empresas contábeis com usuários ilimitados e mais de 1.000 clientes.',
            price: 5000,
            additionalClientPrice: 1,
            maxUsers: 9999,
            maxClients: 9999,
            maxDocumentsPerMonth: 9999999,
            maxTransactionsPerMonth: 9999999,
            storageLimit: 100,
            features: [
              'Suporte VIP 24/7',
              'Upload de documentos',
              'Processamento de extratos bancários',
              'Geração de arquivos para importação',
              'Dashboard completo',
              'Gestão de clientes',
              'Relatórios avançados',
              'API personalizada para integração',
              'Múltiplos layouts de exportação',
              'Acesso a recursos beta',
              'Consultor dedicado',
              'Treinamento personalizado'
            ],
            planType: 'business_premium',
            isActive: true
          }
        ]
      };
    }
  },
  getPlans: async () => {
    const response = await api.get('/plans');
    return response.data;
  },
  getPlanById: async (id: string) => {
    const response = await api.get(`/plans/${id}`);
    return response.data;
  },
};

// Serviços de documentos
export const documentService = {
  getDocuments: async (filters?: any) => {
    const response = await api.get('/documents', { params: filters });
    return response.data;
  },
  getDocumentById: async (id: string) => {
    const response = await api.get(`/documents/${id}`);
    return response.data;
  },
  uploadDocument: async (formData: FormData) => {
    const response = await api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  deleteDocument: async (id: string) => {
    const response = await api.delete(`/documents/${id}`);
    return response.data;
  },
};

export default api;