const { Model, DataTypes } = require('sequelize');

class Document extends Model {
  static init(sequelize) {
    super.init({
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      file_path: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      file_size: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          min: 0
        }
      },
      file_type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          notEmpty: true
        }
      },
      status: {
        type: DataTypes.ENUM('pending', 'processing', 'processed', 'approved', 'rejected', 'error'),
        defaultValue: 'pending',
        allowNull: false
      },
      user_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        }
      },
      company_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id'
        }
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {}
      },
      processing_result: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {}
      }
    }, {
      sequelize,
      tableName: 'documents',
      hooks: {
        beforeCreate: async (document) => {
          // Validar o tamanho máximo do arquivo (20MB)
          const maxFileSize = 20 * 1024 * 1024; // 20MB em bytes
          if (document.file_size > maxFileSize) {
            throw new Error('O arquivo excede o tamanho máximo permitido (20MB)');
          }
        }
      }
    });
  }

  static associate(models) {
    this.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    this.belongsTo(models.Company, { foreignKey: 'company_id', as: 'company' });
  }
}

module.exports = Document; 