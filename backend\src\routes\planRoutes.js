const express = require('express');
const router = express.Router();
const planController = require('../controllers/planController');
const authMiddleware = require('../middlewares/authMiddleware');

// Rota pública para obter todos os planos disponíveis
router.get('/public', planController.getPublicPlans);

// Rotas protegidas - requerem autenticação
router.use(authMiddleware);

// Obter todos os planos
router.get('/', planController.getAllPlans);

// Obter um plano específico
router.get('/:id', planController.getPlanById);

// Criar um novo plano (apenas admin)
router.post('/', planController.createPlan);

// Atualizar um plano (apenas admin)
router.put('/:id', planController.updatePlan);

// Desativar um plano (apenas admin)
router.delete('/:id', planController.deletePlan);

module.exports = router; 