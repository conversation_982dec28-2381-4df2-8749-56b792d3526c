import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FileUpload as ImportIcon,
  FileDownload as ExportIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import accountingRuleService, {
  AccountingRule,
  AccountingRuleFilter,
  CreateAccountingRuleData,
  UpdateAccountingRuleData
} from '../../services/accountingRuleService';
import RegexTester from '../../components/RegexTester';
import { useSnackbar } from 'notistack';

const AccountingRules: React.FC = () => {
  // Estados para listagem e paginação
  const [rules, setRules] = useState<AccountingRule[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [loading, setLoading] = useState(false);

  // Estados para filtros
  const [filters, setFilters] = useState<AccountingRuleFilter>({
    search: '',
    isActive: true
  });

  // Estados para modal de criação/edição
  const [openModal, setOpenModal] = useState(false);
  const [editingRule, setEditingRule] = useState<AccountingRule | null>(null);
  const [formData, setFormData] = useState<CreateAccountingRuleData>({
    name: '',
    description: '',
    pattern: '',
    accountCode: '',
    isDebit: false,
    isActive: true,
    priority: 0,
    category: '',
    tags: []
  });

  const { enqueueSnackbar } = useSnackbar();

  // Carrega a lista de regras
  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await accountingRuleService.getRules({
        ...filters,
        page: page + 1,
        limit
      });
      setRules(response.rules);
      setTotal(response.total);
    } catch (error) {
      enqueueSnackbar('Erro ao carregar regras contábeis', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRules();
  }, [page, limit, filters]);

  // Handlers para paginação
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handlers para filtros
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, search: event.target.value }));
    setPage(0);
  };

  // Handlers para modal
  const handleOpenModal = (rule?: AccountingRule) => {
    if (rule) {
      setEditingRule(rule);
      setFormData({
        name: rule.name,
        description: rule.description || '',
        pattern: rule.pattern,
        accountCode: rule.accountCode,
        isDebit: rule.isDebit,
        isActive: rule.isActive,
        priority: rule.priority,
        category: rule.category || '',
        tags: rule.tags || []
      });
    } else {
      setEditingRule(null);
      setFormData({
        name: '',
        description: '',
        pattern: '',
        accountCode: '',
        isDebit: false,
        isActive: true,
        priority: 0,
        category: '',
        tags: []
      });
    }
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingRule(null);
    setFormData({
      name: '',
      description: '',
      pattern: '',
      accountCode: '',
      isDebit: false,
      isActive: true,
      priority: 0,
      category: '',
      tags: []
    });
  };

  // Handler para salvar regra
  const handleSave = async () => {
    try {
      if (editingRule) {
        await accountingRuleService.updateRule(editingRule.id, formData as UpdateAccountingRuleData);
        enqueueSnackbar('Regra atualizada com sucesso', { variant: 'success' });
      } else {
        await accountingRuleService.createRule(formData);
        enqueueSnackbar('Regra criada com sucesso', { variant: 'success' });
      }
      handleCloseModal();
      loadRules();
    } catch (error) {
      enqueueSnackbar('Erro ao salvar regra', { variant: 'error' });
    }
  };

  // Handler para excluir regra
  const handleDelete = async (id: string) => {
    if (!window.confirm('Tem certeza que deseja excluir esta regra?')) return;
    
    try {
      await accountingRuleService.deleteRule(id);
      enqueueSnackbar('Regra excluída com sucesso', { variant: 'success' });
      loadRules();
    } catch (error) {
      enqueueSnackbar('Erro ao excluir regra', { variant: 'error' });
    }
  };

  // Handler para importar regras
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await accountingRuleService.importRules(file);
      enqueueSnackbar(`${result.imported} regras importadas com sucesso`, { variant: 'success' });
      if (result.errors.length > 0) {
        enqueueSnackbar(`${result.errors.length} erros encontrados`, { variant: 'warning' });
      }
      loadRules();
    } catch (error) {
      enqueueSnackbar('Erro ao importar regras', { variant: 'error' });
    }
  };

  // Handler para exportar regras
  const handleExport = async () => {
    try {
      const blob = await accountingRuleService.exportRules(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'regras-contabeis.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      enqueueSnackbar('Erro ao exportar regras', { variant: 'error' });
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h4">Regras Contábeis</Typography>
            <Box>
              <input
                type="file"
                accept=".csv,.xlsx"
                style={{ display: 'none' }}
                id="import-file"
                onChange={handleImport}
              />
              <label htmlFor="import-file">
                <Button
                  component="span"
                  startIcon={<ImportIcon />}
                  sx={{ mr: 1 }}
                >
                  Importar
                </Button>
              </label>
              <Button
                startIcon={<ExportIcon />}
                onClick={handleExport}
                sx={{ mr: 1 }}
              >
                Exportar
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenModal()}
              >
                Nova Regra
              </Button>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Pesquisar"
                  value={filters.search}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'action.active' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={filters.isActive}
                      onChange={(e) => setFilters(prev => ({ ...prev, isActive: e.target.checked }))}
                    />
                  }
                  label="Mostrar apenas regras ativas"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Nome</TableCell>
                  <TableCell>Padrão</TableCell>
                  <TableCell>Conta Contábil</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell>Prioridade</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rules.map((rule) => (
                  <TableRow key={rule.id}>
                    <TableCell>{rule.name}</TableCell>
                    <TableCell>{rule.pattern}</TableCell>
                    <TableCell>{rule.accountCode}</TableCell>
                    <TableCell>{rule.isDebit ? 'Débito' : 'Crédito'}</TableCell>
                    <TableCell>{rule.priority}</TableCell>
                    <TableCell>
                      <Chip
                        label={rule.isActive ? 'Ativo' : 'Inativo'}
                        color={rule.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Editar">
                        <IconButton onClick={() => handleOpenModal(rule)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Excluir">
                        <IconButton onClick={() => handleDelete(rule.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <TablePagination
              component="div"
              count={total}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={limit}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="Itens por página"
            />
          </TableContainer>
        </Grid>
      </Grid>

      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingRule ? 'Editar Regra Contábil' : 'Nova Regra Contábil'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrição"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <RegexTester
                initialPattern={formData.pattern}
                onPatternChange={(pattern) => setFormData(prev => ({ ...prev, pattern }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Código da Conta"
                value={formData.accountCode}
                onChange={(e) => setFormData(prev => ({ ...prev, accountCode: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Categoria"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Prioridade"
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isDebit}
                    onChange={(e) => setFormData(prev => ({ ...prev, isDebit: e.target.checked }))}
                  />
                }
                label="É um débito?"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label="Regra ativa"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal}>Cancelar</Button>
          <Button onClick={handleSave} variant="contained" color="primary">
            Salvar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AccountingRules; 