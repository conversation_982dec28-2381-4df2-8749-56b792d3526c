const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Obter todos os usuários (admin, manager)
router.get('/', authorize(['admin', 'manager']), userController.getUsers);

// Criar um novo usuário (admin)
router.post('/', authorize(['admin']), userController.createUser);

// Obter um usuário pelo ID
router.get('/:id', userController.getUserById);

// Atualizar um usuário
router.put('/:id', userController.updateUser);

// Alterar senha do usuário
router.put('/:id/change-password', userController.changePassword);

// Excluir um usuário (admin)
router.delete('/:id', authorize(['admin']), userController.deleteUser);

module.exports = router; 