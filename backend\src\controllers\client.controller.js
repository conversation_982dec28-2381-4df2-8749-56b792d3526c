const { Client, Company } = require('../models');
const { Op } = require('sequelize');

/**
 * Obter todos os clientes
 * @route GET /api/clients
 * @access Private
 */
exports.getClients = async (req, res) => {
  try {
    // Parâmetros de paginação e filtros
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    
    // Filtros
    const filters = {};
    
    // Filtro por empresa (se não for admin, filtrar pela empresa do usuário)
    if (req.user.role !== 'admin' && req.user.company_id) {
      filters.company_id = req.user.company_id;
    } else if (req.query.company_id) {
      filters.company_id = req.query.company_id;
    }
    
    // Filtro por status (ativo/inativo)
    if (req.query.is_active !== undefined) {
      filters.is_active = req.query.is_active === 'true';
    }
    
    // Filtro por tipo de documento
    if (req.query.document_type) {
      filters.document_type = req.query.document_type;
    }
    
    // Filtro por busca (nome ou número do documento)
    if (req.query.search) {
      filters[Op.or] = [
        { name: { [Op.like]: `%${req.query.search}%` } },
        { document_number: { [Op.like]: `%${req.query.search}%` } }
      ];
    }
    
    // Buscar clientes
    const { count, rows: clients } = await Client.findAndCountAll({
      where: filters,
      include: [
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });
    
    // Calcular total de páginas
    const totalPages = Math.ceil(count / limit);
    
    res.json({
      success: true,
      data: {
        clients,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('Erro ao obter clientes:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao obter clientes', 
      error: error.message 
    });
  }
};

/**
 * Obter um cliente pelo ID
 * @route GET /api/clients/:id
 * @access Private
 */
exports.getClientById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Buscar cliente
    const client = await Client.findByPk(id, {
      include: [
        { model: Company, as: 'company', attributes: ['id', 'name'] }
      ]
    });
    
    if (!client) {
      return res.status(404).json({ 
        success: false,
        message: 'Cliente não encontrado' 
      });
    }
    
    // Verificar se o usuário tem acesso ao cliente
    if (req.user.role !== 'admin' && req.user.company_id !== client.company_id) {
      return res.status(403).json({ 
        success: false,
        message: 'Acesso não autorizado a este cliente' 
      });
    }
    
    res.json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Erro ao obter cliente:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao obter cliente', 
      error: error.message 
    });
  }
};

/**
 * Criar um novo cliente
 * @route POST /api/clients
 * @access Private
 */
exports.createClient = async (req, res) => {
  try {
    const { 
      name, 
      document_type, 
      document_number, 
      email, 
      phone, 
      address_street,
      address_number,
      address_complement,
      address_neighborhood,
      address_city,
      address_state,
      address_zip_code,
      company_id
    } = req.body;
    
    // Verificar se o número do documento já está em uso
    const existingClient = await Client.findOne({ 
      where: { document_number } 
    });
    
    if (existingClient) {
      return res.status(400).json({ 
        success: false,
        message: 'Número de documento já está em uso' 
      });
    }
    
    // Definir a empresa do cliente
    let clientCompanyId = company_id;
    
    // Se não for admin, usar a empresa do usuário
    if (req.user.role !== 'admin') {
      clientCompanyId = req.user.company_id;
    }
    
    // Criar cliente
    const client = await Client.create({
      name,
      document_type,
      document_number,
      email,
      phone,
      address_street,
      address_number,
      address_complement,
      address_neighborhood,
      address_city,
      address_state,
      address_zip_code,
      company_id: clientCompanyId
    });
    
    res.status(201).json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Erro ao criar cliente:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao criar cliente', 
      error: error.message 
    });
  }
};

/**
 * Atualizar um cliente
 * @route PUT /api/clients/:id
 * @access Private
 */
exports.updateClient = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      email, 
      phone, 
      address_street,
      address_number,
      address_complement,
      address_neighborhood,
      address_city,
      address_state,
      address_zip_code,
      is_active
    } = req.body;
    
    // Buscar cliente
    const client = await Client.findByPk(id);
    
    if (!client) {
      return res.status(404).json({ 
        success: false,
        message: 'Cliente não encontrado' 
      });
    }
    
    // Verificar se o usuário tem acesso ao cliente
    if (req.user.role !== 'admin' && req.user.company_id !== client.company_id) {
      return res.status(403).json({ 
        success: false,
        message: 'Acesso não autorizado a este cliente' 
      });
    }
    
    // Atualizar cliente
    await client.update({
      name,
      email,
      phone,
      address_street,
      address_number,
      address_complement,
      address_neighborhood,
      address_city,
      address_state,
      address_zip_code,
      is_active
    });
    
    res.json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Erro ao atualizar cliente:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao atualizar cliente', 
      error: error.message 
    });
  }
};

/**
 * Excluir um cliente
 * @route DELETE /api/clients/:id
 * @access Private
 */
exports.deleteClient = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Buscar cliente
    const client = await Client.findByPk(id);
    
    if (!client) {
      return res.status(404).json({ 
        success: false,
        message: 'Cliente não encontrado' 
      });
    }
    
    // Verificar se o usuário tem acesso ao cliente
    if (req.user.role !== 'admin' && req.user.company_id !== client.company_id) {
      return res.status(403).json({ 
        success: false,
        message: 'Acesso não autorizado a este cliente' 
      });
    }
    
    // Excluir cliente
    await client.destroy();
    
    res.json({
      success: true,
      message: 'Cliente excluído com sucesso'
    });
  } catch (error) {
    console.error('Erro ao excluir cliente:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erro ao excluir cliente', 
      error: error.message 
    });
  }
};
