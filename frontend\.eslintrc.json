{"env": {"browser": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn"], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}, "settings": {"react": {"version": "detect"}}}